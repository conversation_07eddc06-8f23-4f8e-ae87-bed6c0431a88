import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      screens: {
        xs: "500px",
      },
      colors: {
        gray: {
          100: "#878A8F",
          200: "#29282D",
          300: "#38383D",
          400: "#b5b5b8",
          500: "#848B96",
          600: "#363a42",
          700: "#2d3138",
        },
        dark: {
          100: "#19191C",
          200: "#17191e",
          300: "#282a30",
          400: "#17181c",
        },
        red: {
          100: "#FF0066",
        },
        blue: {
          100: "#004AF7",
          200: "#34aaf8",
        },
        purple: {
          100: "#9747FF",
        },
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
        "btn-gradient":
          "linear-gradient(94.47deg, #FF068F -0.02%, #FF064C 99.79%)",
        "purple-pink-gradient":
          "linear-gradient(90deg, #9747FF 0%, #FF53C8 100%)",
        "gradient-blue": "linear-gradient(90deg, #4C82FF 0%, #2DEAFF 100%)",
        "gradient-red": "linear-gradient(90deg, #FF4A3A 0%, #FF0066 100%)",
        "balance-gradient":
          "linear-gradient(91.05deg, #FF0066 0%, #9747FF 99.1%)",
        "gradient-dark-blue":
          "linear-gradient(180deg, #1c2438 0%, #090e1e 99.1%)",
        "gradient-purple-blue":
          "linear-gradient(90deg, #744bf6 0%, #5da0f6 100%)",
      },
    },
  },
  plugins: [],
};
export default config;
