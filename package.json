{"name": "legitapp-client-web-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/cssinjs": "^1.21.0", "@ant-design/icons": "^5.4.0", "@react-spring/web": "^9.7.5", "@stripe/stripe-js": "^5.4.0", "@u-wave/react-vimeo": "^0.9.11", "antd": "^5.20.0", "bowser": "^2.11.0", "clsx": "^2.1.1", "framer-motion": "^11.3.21", "html-react-parser": "^5.1.12", "isomorphic-unfetch": "^4.0.2", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "next": "14.2.5", "next-redux-wrapper": "^8.1.0", "nextjs-progressbar": "^0.0.16", "photoswipe": "^5.4.4", "qs": "^6.13.0", "react": "^18", "react-confetti": "^6.1.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18", "react-ga": "^3.3.1", "react-gtm-module": "^2.0.11", "react-intersection-observer": "^9.13.0", "react-intl": "^6.6.8", "react-markdown": "^9.0.1", "react-photoswipe-gallery": "^3.0.2", "react-qr-code": "^2.0.15", "react-redux": "^9.1.2", "react-zendesk": "^0.1.13", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "schema-dts": "^1.1.2", "uuid": "^11.1.0", "yet-another-react-lightbox": "^3.23.2"}, "devDependencies": {"@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "@reduxjs/toolkit": "^2.2.7", "@types/lodash": "^4.17.7", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.5.13", "@types/node": "^20", "@types/qs": "^6.9.15", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "sass": "^1.77.8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}