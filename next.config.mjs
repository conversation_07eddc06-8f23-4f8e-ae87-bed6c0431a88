/** @type {import('next').NextConfig} */
import * as url from "node:url";
import * as path from "path";
const __dirname = url.fileURLToPath(new URL(".", import.meta.url));

const isDevelopment = false;

const nextConfig = {
  reactStrictMode: false,
  sassOptions: {
    fiber: false,
    includePaths: [path.join(__dirname, "styles")],
    additionalData: `@use "sass:math";@import "src/styles/_common.scss";@import "src/styles/mixins.scss";`,
  },
  transpilePackages: [
    "antd",
    "@ant-design",
    "rc-util",
    "rc-pagination",
    "rc-picker",
    "rc-notification",
    "rc-tooltip",
  ],
  publicRuntimeConfig: {
    // Will be available on both server and client
    CORE_API_URL: process.env.CORE_API_URL || `https://api-dev.legitapp.com`,
    // APP_URL: process.env.APP_URL || 'http://localhost:3000',
    // NODE_ENV: process.env.NODE_ENV || 'production',
  },
  experimental: {
    scrollRestoration: true,
  },
  i18n: {
    locales: [
      "en",
      "es",
      "zh-Hans",
      "zh-Hant",
      "ja",
      "ko",
      "vi",
      "th",
      "it",
      "da",
      "pl",
      "nl",
      "zh",
    ],
    defaultLocale: "en",
  },
  serverRuntimeConfig: {
    // Will only be available on the server side
  },
  publicRuntimeConfig: {
    // Will be available on both server and client
    API_REVALIDATION_TOKEN: process.env.API_REVALIDATION_TOKEN,
    CORE_API_URL:
      process.env.CORE_API_URL ||
      `https://api-${isDevelopment ? "dev" : "prod"}.legitapp.com/user`,
    APP_URL: process.env.APP_URL || "http://localhost:3000",
    STRIPE_PUBLISHABLE_KEY:
      process.env.STRIPE_PUBLISHABLE_KEY || "pk_test_RgCwUNcOiYx2eVXUhD0Vba9v",
    // NODE_ENV: process.env.NODE_ENV || 'production',
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "legitapp-prod.oss-cn-hongkong.aliyuncs.com",
      },
      {
        protocol: "https",
        hostname: "legitapp-prod.oss-accelerate.aliyuncs.com",
      },
      {
        protocol: "https",
        hostname: "legitapp-dev.oss-cn-hongkong.aliyuncs.com",
      },
      {
        protocol: "https",
        hostname: "legitapp-dev.oss-accelerate.aliyuncs.com",
      },
    ],
  },
  async redirects() {
    return [
      // Basic redirect
      {
        source: "/library",
        destination: "/blog",
        permanent: true,
      },
      {
        source: "/library/:articleSlug",
        destination: "/blog/:articleSlug",
        permanent: true,
      },
      {
        source: "/case/:serviceRequestUUID",
        destination: "/cert/:serviceRequestUUID",
        permanent: true,
      },
      {
        source: "/how-it-works",
        destination: "/products",
        permanent: true,
      },
      {
        source: "/protection-plus",
        destination: "/financial-guarantee",
        permanent: true,
      },
      // legacy app pages
      {
        source: "/app/app-how-it-works",
        destination: "/app/how-it-works",
        permanent: true,
      },
      {
        source: "/app/app-legit-token",
        destination: "/app/legit-token",
        permanent: true,
      },
      {
        source: "/app/app-protectionplus",
        destination: "/app/financial-guarantee",
        permanent: true,
      },
      {
        source: "/app/protection-plus",
        destination: "/app/financial-guarantee",
        permanent: true,
      },
      {
        source: "/app/app-luxe-tags",
        destination: "/app/luxe-tags",
        permanent: true,
      },
      {
        source: "/app/app-tags",
        destination: "/app/kicks-tags",
        permanent: true,
      },
      {
        source: "/app/app-how-it-works",
        destination: "/app/how-it-works",
        permanent: true,
      },
      {
        source: "/portal/home",
        destination: "/portal",
        permanent: true,
      },
    ];
  },
};

export default nextConfig;
