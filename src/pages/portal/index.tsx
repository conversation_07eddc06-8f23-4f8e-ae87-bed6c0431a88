import React, { useEffect } from "react";
import { RootState } from "reducers";
import { useRouter } from "next/router";

import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { fetchHomeData } from "actions/startAuthenticationHome";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import Banner from "components/StartAuthentication/HomePart/Banner";
import FeaturedBrands from "components/StartAuthentication/HomePart/FeaturedBrands";
import Article from "components/StartAuthentication/Article";
import CaseHistory from "components/StartAuthentication/CaseHistory";
import AdsBanner from "components/StartAuthentication/AdsBanner";
import AppSpin from "components/AppSpin";
import { ChooseCategoryByBrandIdPath } from "components/StartAuthentication/constant";

const Portal = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { data, isLoading } = useAppSelector(
    (state: RootState) => state.startAuthenticationHome
  );

  useEffect(() => {
    if (data) return;
    dispatch(fetchHomeData());
  }, [dispatch, data]);

  return (
    <StartAuthenticationHeader>
      <div className="overflow-y-auto w-full flex flex-col gap-5">
        {isLoading && <AppSpin />}

        {!isLoading && data && (
          <>
            {/* banner part */}
            <Banner bannerList={data?.app_banner?.data || []} />
            {/* Featured Brands */}
            <FeaturedBrands
              brandList={data?.product_brand?.data || []}
              onClick={(brand) => {
                router.push(`${ChooseCategoryByBrandIdPath}/${brand.id}`);
              }}
            />
            {/* ads */}
            <AdsBanner />
            {/* article */}
            <Article articleList={data?.article?.data || []} />
            {/* Case History */}
            <CaseHistory />
          </>
        )}
      </div>
    </StartAuthenticationHeader>
  );
};

export default Portal;
