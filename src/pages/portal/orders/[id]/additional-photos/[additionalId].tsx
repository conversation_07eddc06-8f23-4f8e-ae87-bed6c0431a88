import React, { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/router";
import { LeftOutlined } from "@ant-design/icons";
import clsx from "clsx";

import useAppSelector from "hooks/useAppSelector";
import useOrderDetail from "hooks/useOrderDetail";
import { RootState } from "reducers";
import AppSpin from "components/AppSpin";
import DetailCardInfo from "components/StartAuthentication/OrdersComp/DetailCardInfo";
import OrderDetailSectionContainer from "components/StartAuthentication/OrdersComp/OrderDetailSectionContainer";
import PhotosList from "components/StartAuthentication/new-authentication/PhotosList";
import { IServiceGuideline, IUploadPhotoItem } from "types/orders";
import {
  ordersPath,
  SERVICE_REQUEST_ADDITIONAL_STATUS,
} from "components/StartAuthentication/constant";
import apiCore from "utils/apiCore";
import { showErrorPopupMessage, showInfoMessage } from "utils/message";
import { parseError } from "utils/error";
import ViewPhotoInstructionModal from "components/StartAuthentication/new-authentication/ViewPhotoInstructionModal";
import GalleryImage from "components/GalleryImage";

const AdditionalPhotosPage = () => {
  const router = useRouter();
  const { locale = "en" } = router;
  const { id, additionalId } = router.query;
  const { accessToken } = useAppSelector((state: RootState) => state.app);

  const [uploadPhotoList, setUploadPhotoList] = useState<IUploadPhotoItem[]>(
    []
  );

  const [remark, setUserRemark] = useState("");

  const [isSubmitLoading, setIsSubmitLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [guidelineList, setGuidelineList] = useState<IServiceGuideline[]>([]);
  const [isFetchingGuideline, setIsFetchingGuideline] = useState(false);
  const [isUploadingImages, setIsUploadingImages] = useState(false); // is exist any image uploading

  const { orderDetail, isLoading } = useOrderDetail({
    id: id as string,
    accessToken,
  });

  const fetchGuidelineList = async () => {
    try {
      setIsFetchingGuideline(true);
      const res = await apiCore.get(
        null,
        "v1/service_guideline",
        {
          service_guideline_set_id: service_guideline_set_id,
        },
        accessToken
      );
      setGuidelineList(res.data);
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsFetchingGuideline(false);
    }
  };

  useEffect(() => {
    if (isModalOpen && !guidelineList.length) {
      fetchGuidelineList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen, guidelineList.length]);

  const {
    product_brand,
    product_model,
    service_request_additional,
    uuid,
    result,
    category_id,
    created_at,
    completed_at,
    service_level_minute,
    status,
    service_guideline_set_id,
  } = orderDetail || {};

  const { icon_image_url: brandIconUrl, title: brandTitle } =
    product_brand || {};

  const { icon_image_url: modelIconUrl, title: modelTitle } =
    product_model || {};

  const currentAdditional = useMemo(() => {
    return service_request_additional?.find(
      (item) => item.id === Number(additionalId)
    );
  }, [service_request_additional, additionalId]);

  const serviceRequestAdditionalPending = useMemo(
    () =>
      service_request_additional?.filter(
        (item) => item.status === SERVICE_REQUEST_ADDITIONAL_STATUS.PENDING
      ) || [],
    [service_request_additional]
  );

  const requiredPhotoParts = useMemo(() => {
    return (
      serviceRequestAdditionalPending[0]?.checker_additional_part_image_required
        ?.split(",")
        .map((type) => type.trim()) || []
    );
  }, [serviceRequestAdditionalPending]);

  const checkerAdditionalRemark = useMemo(() => {
    return service_request_additional?.[service_request_additional.length - 1]
      ?.checker_additional_remark;
  }, [service_request_additional]);

  const examplePhotoList = useMemo(() => {
    return (
      service_request_additional?.[
        service_request_additional.length - 1
      ]?.checker_additional_example_image_urls
        ?.split(",")
        ?.map((img) => ({
          image_url: img.trim(),
        })) || []
    );
  }, [service_request_additional]);

  const handleUploadingStateChange = (isUploading: boolean) => {
    setIsUploadingImages(isUploading);
  };

  const handleSubmit = async () => {
    if (
      isSubmitLoading ||
      !serviceRequestAdditionalPending?.[0]?.service_request_id
    )
      return;

    if (isUploadingImages) {
      showErrorPopupMessage("Please wait for images to finish uploading");
      return;
    }

    if (!uploadPhotoList.length) {
      showErrorPopupMessage("Please upload at least one photo.");
      return;
    }

    if (
      uploadPhotoList.length <
      serviceRequestAdditionalPending[0].service_placeholder.length
    ) {
      showErrorPopupMessage("Please upload all required photos.");
      return;
    }

    const params = {
      images: uploadPhotoList?.map((item: IUploadPhotoItem) => ({
        ...item,
        system_image_remark: JSON.stringify(item.system_image_remark),
      })),
      user_remark: remark || null,
    };

    try {
      setIsSubmitLoading(true);
      await apiCore.post(
        null,
        `v1/service_request/${serviceRequestAdditionalPending[0].service_request_id}/photo`,
        params,
        accessToken
      );
      showInfoMessage("Successfully Submitted.");
      router.push(`${ordersPath}/${id}`);
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsSubmitLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <AppSpin />
      </div>
    );
  }

  if (!orderDetail || !currentAdditional) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center text-white">
        <div>Order or additional photos request not found</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="max-w-screen-lg m-auto px-4 md:px-[44px] flex justify-between items-center py-8">
        <div className="cursor-pointer" onClick={() => router.back()}>
          <LeftOutlined style={{ fontSize: "24px" }} />
        </div>
        <div className="text-center">
          <div className="text-lg font-medium">#{uuid} - Additional Photos</div>
        </div>
        <div></div>
      </div>

      {/* Content */}
      <div className="max-w-screen-lg m-auto px-4 md:px-[44px] pb-8">
        {/* product info */}
        <OrderDetailSectionContainer>
          <DetailCardInfo
            brandIconUrl={brandIconUrl}
            modelIconUrl={modelIconUrl}
            brandTitle={brandTitle}
            modelTitle={modelTitle}
            status={status}
            result={result}
            categoryId={category_id}
            createdAt={created_at}
            serviceLevelMinute={service_level_minute}
            completedAt={completed_at}
            uuid={uuid}
          />
        </OrderDetailSectionContainer>

        {!!checkerAdditionalRemark && (
          <OrderDetailSectionContainer title="Reviews from Authenticators">
            <div className="whitespace-pre-line">{checkerAdditionalRemark}</div>
          </OrderDetailSectionContainer>
        )}

        <OrderDetailSectionContainer title="Additional Photos Requested">
          <div className="flex flex-wrap gap-2">
            {requiredPhotoParts.map((part, index) => (
              <div
                key={index}
                className="bg-red-800 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
              >
                {part}
              </div>
            ))}
          </div>
        </OrderDetailSectionContainer>

        {!!examplePhotoList?.length && (
          <OrderDetailSectionContainer title="Examples of Photo Requested">
            <GalleryImage imageList={examplePhotoList} isShowMarker={false} />
          </OrderDetailSectionContainer>
        )}

        {!!serviceRequestAdditionalPending?.[0]?.service_placeholder
          ?.length && (
          <OrderDetailSectionContainer
            title="Additional Photos Upload"
            rightContent={
              <button
                className="bg-red-800 text-white px-4 py-2 rounded text-sm flex items-center gap-2"
                onClick={() => setIsModalOpen(true)}
              >
                <img src="/order/icon-guildeline.png" className="w-4 h-4" />
                Instructions
              </button>
            }
          >
            <>
              <PhotosList
                title=""
                list={serviceRequestAdditionalPending[0].service_placeholder}
                locale={locale}
                isOptional={true}
                setUploadPhotoList={setUploadPhotoList}
                onUploadingStateChange={handleUploadingStateChange}
              />
              <ViewPhotoInstructionModal
                setIsModalOpen={setIsModalOpen}
                isModalOpen={isModalOpen}
                guidelineList={guidelineList}
                locale={locale}
                loading={isFetchingGuideline}
              />
            </>
          </OrderDetailSectionContainer>
        )}

        <OrderDetailSectionContainer title="Remarks" isShowOptional={true}>
          <div className="flex flex-col gap-5">
            <textarea
              rows={5}
              placeholder="Text Here"
              className="md:text-base text-xs resize-none outline-none bg-transparent border-gray-200 border w-full rounded-md px-2 py-2 placeholder-gray-100"
              value={remark}
              onChange={(e) => {
                setUserRemark(e.target.value);
              }}
            />
          </div>
        </OrderDetailSectionContainer>
        <OrderDetailSectionContainer>
          <button
            onClick={handleSubmit}
            disabled={isSubmitLoading || isUploadingImages}
            className={clsx(
              "w-full text-center md:text-xl font-bold md:py-4 py-2 rounded-lg",
              isSubmitLoading || isUploadingImages
                ? "opacity-50 cursor-not-allowed bg-gray-500"
                : "cursor-pointer bg-gradient-red"
            )}
          >
            {isUploadingImages
              ? "Images Uploading..."
              : isSubmitLoading
              ? "Submitting..."
              : "SUBMIT"}
          </button>
        </OrderDetailSectionContainer>
      </div>
    </div>
  );
};

export default AdditionalPhotosPage;
