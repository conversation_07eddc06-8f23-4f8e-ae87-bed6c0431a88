import React, { useEffect, useState, useRef } from "react";
import { useRouter } from "next/router";
import { LeftOutlined } from "@ant-design/icons";

import useOrderDetail from "hooks/useOrderDetail";
import useUserHook from "hooks/useUser";
import useServiceRequestMessages from "hooks/useServiceRequestMessages";
import AppSpin from "components/AppSpin";
import ConversationMessages from "components/StartAuthentication/OrdersComp/ConversationMessages";
import BottomMsgInput from "components/StartAuthentication/OrdersComp/BottomMsgInput";
import apiCore from "utils/apiCore";
import { IServiceRequestMessage } from "types/orders";

const Conversation = () => {
  const router = useRouter();
  const { locale = "", defaultLocale = "en" } = router;
  const { id } = router.query;
  const { accessToken, user } = useUserHook();

  const { orderDetail, isLoading: isOrderLoading } = useOrderDetail({
    id: id as string,
    accessToken,
  });

  const { messages, isLoading: isMessagesLoading } = useServiceRequestMessages({
    serviceRequestId: id as string,
    accessToken,
  });

  const [currentMsgs, setCurrentMsgs] = useState<
    Partial<IServiceRequestMessage>[]
  >([]);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (messages) {
      setCurrentMsgs(messages);
    }
  }, [messages]);

  useEffect(() => {
    scrollToBottom();
  }, [currentMsgs]);

  const scrollToBottom = (smooth = false) => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
    }
  };

  const { product_brand, product_model, uuid, service_request_image, status } =
    orderDetail || {};

  const modelIconUrl = product_model?.icon_image_url;
  const brandTitle = product_brand?.title;
  const modelTitle = product_model?.title;

  if (isOrderLoading || isMessagesLoading) {
    return <AppSpin />;
  }

  const handleSendMessage = async (msg: string) => {
    if (!accessToken) return;

    const newMessage = {
      id: +new Date() + Math.floor(Math.random() * 1000),
      user_id: user?.id,
      content: msg,
      created_at: new Date().toISOString(),
    };
    setCurrentMsgs([newMessage, ...currentMsgs]);

    await apiCore.post(
      null,
      "v1/service_request_message",
      {
        service_request_id: id,
        content: msg,
      },
      accessToken
    );
  };

  return (
    <div className="h-screen flex flex-col max-w-screen-lg m-auto text-white">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 z-10 bg-black">
        <div className="max-w-screen-lg m-auto md:h-60 h-52">
          <div className="md:px-[44px] px-4 py-4">
            {/* header */}
            <div className="flex justify-between items-center mb-4">
              <div className="cursor-pointer" onClick={() => router.back()}>
                <LeftOutlined style={{ fontSize: "24px" }} />
              </div>
              <div className="font-bold">LEGIT Authenticator</div>
              <div></div>
            </div>

            {/* product info */}
            <div className="overflow-hidden">
              <div className="flex items-center gap-4">
                <div className="md:w-16 md:h-16 w-12 h-12">
                  <img
                    src={modelIconUrl}
                    alt={modelTitle}
                    className="w-full h-full"
                  />
                </div>
                <div>
                  <div className="text-sm text-gray-100">#{uuid}</div>
                  <div className="flex flex-col">
                    <div className="md:text-base text-sm font-bold">
                      {brandTitle || "-"}
                    </div>
                    <div className="md:text-base text-sm font-bold">
                      {modelTitle || "-"}
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div className="flex gap-4 mt-4 overflow-x-auto overflow-y-hidden hide-scrollbar">
                  {service_request_image?.map((image) => (
                    <div
                      key={image.id}
                      className="md:w-20 md:h-20 w-12 h-12 flex-shrink-0"
                    >
                      <img
                        src={image.image_url}
                        alt={image.type}
                        className="w-full h-full object-cover rounded"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div
        ref={scrollAreaRef}
        className="flex-1 overflow-y-auto pt-52 md:pt-60 pb-24 scroll-smooth"
      >
        <div className="md:px-[44px] px-4">
          <ConversationMessages
            messages={currentMsgs || []}
            locale={locale}
            defaultLocale={defaultLocale}
            user={user}
            id={id as string}
          />
        </div>
      </div>

      {/* Fixed Bottom Input */}
      <BottomMsgInput handleSendMessage={handleSendMessage} status={status} />
    </div>
  );
};

export default Conversation;
