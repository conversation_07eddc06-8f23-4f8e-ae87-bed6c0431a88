/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import { RootState } from "reducers";

import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { IProductCategory } from "types/orders";
import NavigationHeader from "components/NavigationHeader";
import { chooseModalPath } from "components/StartAuthentication/constant";
import { fetchBrandList, reset } from "actions/productBrand";
import FeaturedBrands from "components/StartAuthentication/HomePart/FeaturedBrands";
import AppSpin from "components/AppSpin";
import { IAppBrand } from "types/app";

const ChooseBrand = () => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const productBrand: any = useAppSelector(
    (state: RootState) => state.productBrand
  );
  const { items, isFetchItemsLoading } = productBrand;

  useEffect(() => {
    if (id) {
      dispatch(
        fetchBrandList({
          categoryId: id as string,
        })
      );
    }
  }, [dispatch, id]);

  useEffect(() => {
    return () => {
      dispatch(reset());
    };
  }, []);

  return (
    <div className="text-white">
      <NavigationHeader progress="40%" />
      <div className="max-w-screen-lg m-auto md:py-10 py-4 md:px-12 px-4">
        <div className="text-2xl md:text-3xl font-bold text-center md:mb-10 mb-4">
          Choose a Brand
        </div>

        {/* loading */}
        {(!items || items.length === 0) && isFetchItemsLoading && <AppSpin />}

        {/* Featured Brands */}
        {items?.some((brand: IAppBrand) => brand.featured > 0) && (
          <FeaturedBrands
            brandList={
              items
                .filter((brand: IAppBrand) => brand.featured > 0)
                .sort(
                  (a: IAppBrand, b: IAppBrand) => b.featured - a.featured
                ) || []
            }
            onClick={(brand) => {
              router.push(`${chooseModalPath}/${id}/${brand.id}`);
            }}
          />
        )}

        <div className="mt-4">
          {items?.map((brand: IProductCategory) => (
            <div
              key={brand.id}
              className="cursor-pointer border-b border-gray-200 flex items-center gap-7"
              onClick={() => {
                router.push(`${chooseModalPath}/${id}/${brand.id}`);
              }}
            >
              <div className="w-20 h-20 md:w-24 md:h-24 flex justify-center items-center">
                <Image
                  src={brand.icon_image_url}
                  width={96}
                  height={96}
                  alt={brand.title}
                />
              </div>
              <div className="font-semibold md:text-base text-xs">
                {brand.title}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChooseBrand;
