import React, { useEffect, useState } from "react";
import WalletComp from "components/StartAuthentication/WalletComp";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import { useRouter } from "next/router";
import { showSuccessPopupMessage } from "utils/message";
import PaymentSuccessModal from "components/PaymentSuccessModal";

const Wallet = () => {
  const router = useRouter();
  const { status, tokenName, tokenId, amount } = router.query;
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (status === "success") {
      showSuccessPopupMessage("Payment successful");
      setIsModalOpen(true);
    }
  }, [status]);
  return (
    <div className="min-h-screen">
      <StartAuthenticationHeader>
        <WalletComp />
      </StartAuthenticationHeader>
      <PaymentSuccessModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        tokenName={tokenName as string}
        tokenId={tokenId as string}
        amount={amount as string}
      />
    </div>
  );
};

export default Wallet;
