import React, { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/router";

import { IProductCategory } from "types/orders";
import { getLocalisedField } from "utils/locale";
import apiCore from "utils/apiCore";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import NavigationHeader from "components/NavigationHeader";
import { chooseModalPath } from "components/StartAuthentication/constant";
import AppSpin from "components/AppSpin";

const ChooseCategoryByBrandId = () => {
  const router = useRouter();
  const { locale } = router;
  const { brand_id } = router.query;

  const [categoryList, setCategoryList] = useState<IProductCategory[]>([]);
  const [isFetchItemsLoading, setIsFetchItemsLoading] = useState(false);

  const fetchCategoryList = useCallback(async () => {
    try {
      setIsFetchItemsLoading(true);
      const result = await apiCore.get(null, "v1/product_category", {
        brand_id,
      });
      setCategoryList(result.data);
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsFetchItemsLoading(false);
    }
  }, [brand_id]);

  useEffect(() => {
    fetchCategoryList();
  }, [fetchCategoryList]);

  return (
    <div className="text-white">
      <NavigationHeader hideBackButton progress="20%" />
      <div className="max-w-screen-lg m-auto md:py-10 py-4 md:px-12 px-4">
        <div className="text-2xl md:text-3xl font-bold text-center md:mb-10 mb-4">
          Choose a Category
        </div>

        {/* loading */}
        {categoryList.length === 0 && isFetchItemsLoading && <AppSpin />}

        <div className="grid grid-cols-2 md:grid-cols-3 gap-5">
          {categoryList?.map((category: IProductCategory) => (
            <div
              key={category.id}
              className="cursor-pointer md:h-72 h-40 border border-gray-200 rounded-xl flex flex-col justify-center items-center"
              onClick={() => {
                router.push(`${chooseModalPath}/${category.id}/${brand_id}`);
              }}
            >
              <div className="w-20 h-20 md:w-30 md:h-30 flex justify-center items-center">
                <Image
                  src={category.icon_image_url}
                  width={120}
                  height={120}
                  alt={category.title}
                />
              </div>
              <div className="font-semibold md:mt-7 mt-4 md:text-base text-[10px]">
                {getLocalisedField(category, "title", locale)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChooseCategoryByBrandId;
