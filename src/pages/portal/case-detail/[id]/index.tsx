import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useRouter } from "next/router";

import apiCore from "utils/apiCore";
import { ICaseDetail } from "types/profile";
import CaseDetailCardInfo from "components/StartAuthentication/CaseDetail/CaseDetailCardInfo";
import OrderDetailSectionContainer from "components/StartAuthentication/OrdersComp/OrderDetailSectionContainer";
import AIPoweredReplicaScore from "components/StartAuthentication/OrdersComp/AIPoweredReplicaScore";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import GalleryImage from "components/GalleryImage";
import ReviewsFromAuthenticators from "components/StartAuthentication/OrdersComp/ReviewsFromAuthenticators";
import useAppSelector from "hooks/useAppSelector";

const CaseDetail = () => {
  const router = useRouter();
  const { locale = "", defaultLocale = "" } = router;
  const { id } = router.query;

  const { accessToken } = useAppSelector((state) => state.app);

  const [caseDetail, setCaseDetail] = useState<ICaseDetail | null>(null);
  const fetchCaseDetail = useCallback(async () => {
    if (id) {
      const res = await apiCore.get(
        null,
        `v2/service_feed/uuid/${id}`,
        {},
        accessToken
      );
      setCaseDetail(res);
    }
  }, [id]);

  useEffect(() => {
    fetchCaseDetail();
  }, [fetchCaseDetail]);

  const {
    product_brand,
    product_model,
    service_request_image,
    service_request_result,
    user_liked,
    status,
    result,
    category_id,
    completed_at,
    fake_rating,
  } = caseDetail || {};

  const { icon_image_url: brandIconUrl, title: brandTitle } =
    product_brand || {};

  const { icon_image_url: modelIconUrl, title: modelTitle } =
    product_model || {};

  const markerImage = useMemo(() => {
    return (
      service_request_image
        ?.filter((item) => !!item.marker_image_url)
        .map((item) => ({
          ...item,
          image_url: item.marker_image_url,
        })) || []
    );
  }, [service_request_image]);

  const handleBookmark = useCallback(async () => {
    await apiCore.post(
      null,
      `v2/service_feed/uuid/${id}/like`,
      { like: !user_liked },
      accessToken
    );
    fetchCaseDetail();
  }, [accessToken, fetchCaseDetail, id, user_liked]);

  return (
    <div className="max-w-screen-lg m-auto text-white py-8">
      <BackHeader
        rightContent={
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={handleBookmark}
          >
            {user_liked ? (
              <img
                src="/bookmark_border_like.png"
                alt="bookmarks liked"
                className="w-8 h-8"
              />
            ) : (
              <img
                src="/bookmark_border_empty.png"
                alt="bookmarks empty"
                className="w-8 h-8"
              />
            )}
          </div>
        }
        className="md:mx-[44px] px-4 !mb-0"
        onBack={() => router.back()}
      />
      {/* Case Detail Card Info */}
      <OrderDetailSectionContainer isNeedBorder={false}>
        <CaseDetailCardInfo
          brandIconUrl={brandIconUrl}
          modelIconUrl={modelIconUrl}
          brandTitle={brandTitle}
          modelTitle={modelTitle}
          status={status}
          result={result}
          completedAt={completed_at}
          category_id={category_id}
        />
      </OrderDetailSectionContainer>

      {/* AI-Powered Replica Score */}
      {fake_rating && (
        <OrderDetailSectionContainer
          title="AI-Powered Replica Score"
          isNeedBorder={false}
        >
          <AIPoweredReplicaScore fake_rating={fake_rating} />
        </OrderDetailSectionContainer>
      )}

      {/* Reviews from Authenticators */}
      {!!service_request_result?.length && (
        <OrderDetailSectionContainer title="Reviews from Authenticators">
          <ReviewsFromAuthenticators
            serviceRequestResult={service_request_result}
            locale={locale}
            defaultLocale={defaultLocale}
          />
        </OrderDetailSectionContainer>
      )}

      {/* Uploaded Photos */}
      <OrderDetailSectionContainer isNeedBorder={false}>
        <GalleryImage
          className="!grid-cols-1"
          imageList={markerImage?.concat(service_request_image || []) || []}
          isShowLightbox={false}
        />
      </OrderDetailSectionContainer>
    </div>
  );
};

export default CaseDetail;
