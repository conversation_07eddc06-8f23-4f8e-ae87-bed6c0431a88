import React, { useState, useRef, useEffect } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import clsx from "clsx";
import { CheckCircleOutlined, LoadingOutlined } from "@ant-design/icons";
import { Input } from "antd";

import apiCore from "utils/apiCore";
import { parseError } from "utils/error";
import { showSuccessPopupMessage, showErrorPopupMessage } from "utils/message";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import { accountPath } from "components/StartAuthentication/constant";
import useAppSelector from "hooks/useAppSelector";
import { fetchUser } from "actions/app";
import useAppDispatch from "hooks/useAppDispatch";
import AppSpin from "components/AppSpin";

const PersonalInformation = () => {
  const intl = useIntl();
  const router = useRouter();
  const { user, accessToken, isFetchUserLoading } = useAppSelector(
    (state) => state.app
  );
  const dispatch = useAppDispatch();

  const [name, setName] = useState(user?.name);

  const [profileImageUrl, setProfileImageUrl] = useState(
    user?.profile_image_url
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (user) {
      setName(user.name);
      setProfileImageUrl(user.profile_image_url);
    }
  }, [user]);

  const handleAvatarClick = () => {
    if (isUploadingAvatar) return;
    fileInputRef.current?.click();
  };

  const handleAvatarUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0 || !accessToken) return;

    const file = files[0];

    try {
      setIsUploadingAvatar(true);

      const res = await apiCore.post(null, "v1/asset_image", file, accessToken);

      if (res && res.url) {
        setProfileImageUrl(res.url);
      }
    } catch (error) {
      console.error("Error uploading avatar:", error);
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsUploadingAvatar(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleSave = async () => {
    if (!accessToken) {
      router.push("/login");
      return;
    }

    if (!name?.trim()) {
      showErrorPopupMessage("Please enter your name");
      return;
    }

    try {
      setIsSubmitting(true);

      const updateData: {
        name?: string;
        profile_image_url?: string;
      } = {
        name: name?.trim(),
      };

      if (profileImageUrl && profileImageUrl !== user?.profile_image_url) {
        updateData.profile_image_url = profileImageUrl;
      }

      await apiCore.patch(null, "v1/me", updateData, accessToken);

      showSuccessPopupMessage("Profile updated successfully");
      dispatch(fetchUser({ accessToken }));
      router.push(accountPath);
    } catch (error) {
      console.error("Error updating profile:", error);
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderAvatar = () => {
    if (profileImageUrl) {
      return (
        <div
          className="w-44 h-44 rounded-full bg-cover bg-center bg-no-repeat border-2 border-gray-600"
          style={{
            backgroundImage: `url(${profileImageUrl})`,
          }}
        />
      );
    }

    return (
      <>
        <div className="w-44 h-44 rounded-full bg-dark-100 flex items-center justify-center border-2 border-gray-600">
          <img
            src="/profile/icon_profile_placeholder.png"
            alt="Default Avatar"
          />
        </div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2/5 h-2/5 flex justify-center items-center">
          {isUploadingAvatar ? (
            <LoadingOutlined />
          ) : (
            <img src="/profile/icon_profile_cam.png" alt="Camera" />
          )}
        </div>
      </>
    );
  };

  const isDisabled =
    isSubmitting ||
    (user?.name === name && user?.profile_image_url === profileImageUrl);
  return (
    <StartAuthenticationHeader>
      <BackHeader
        id="account_menu_personal_information"
        onBack={() => router.push(accountPath)}
        rightContent={
          <button
            className={clsx(
              "font-medium",
              isDisabled
                ? "cursor-not-allowed text-gray-100"
                : "cursor-pointer text-red-500"
            )}
            onClick={handleSave}
            disabled={isDisabled}
          >
            {intl.formatMessage({ id: "profile_personal_information_save" })}
          </button>
        }
      />

      {isFetchUserLoading ? (
        <AppSpin />
      ) : (
        <div className="flex flex-col items-center space-y-8">
          {/* Avatar Section */}
          <div className="relative">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleAvatarUpload}
              accept="image/png, image/jpeg, image/jpg"
              className="hidden"
            />

            <div
              className={`relative ${
                isUploadingAvatar ? "cursor-not-allowed" : "cursor-pointer"
              }`}
              onClick={handleAvatarClick}
            >
              {renderAvatar()}

              {/* Upload overlay */}
              {isUploadingAvatar && (
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2/5 h-2/5 flex justify-center items-center">
                  <AppSpin />
                </div>
              )}
            </div>
          </div>

          {/* Form Fields */}
          <div className="w-full space-y-6">
            {/* Name Field */}
            <div className="space-y-2 border-b border-gray-600 pb-1">
              <Input
                type="text"
                value={
                  user?.apple_id
                    ? intl.formatMessage({ id: "apple_id_login" })
                    : name
                }
                disabled={user?.business_verified === 1}
                onChange={(e) => setName(e.target.value)}
                className={clsx(
                  "w-full !bg-transparent !border-none !border-b !border-gray-600 py-2 px-0 !focus:outline-none !focus:border-white !ring-0",
                  user?.business_verified === 1
                    ? "!text-gray-100"
                    : "!text-white"
                )}
                placeholder="Enter your name"
                prefix={
                  <img
                    src="/profile/icon_login_name.png"
                    alt="Name"
                    className="w-6 object-contain mr-2"
                  />
                }
              />
            </div>

            {/* Email Field */}
            <div className="space-y-2 border-b border-gray-600 pb-1">
              <Input
                type="email"
                disabled
                value={user?.email}
                className="w-full !bg-transparent !border-none !text-gray-100 py-2 px-0 !focus:outline-none !focus:border-white !ring-0"
                prefix={
                  <img
                    src="/profile/icon_login_email.png"
                    alt="Email"
                    className="w-6 object-contain mr-2"
                  />
                }
                suffix={<CheckCircleOutlined className="!text-white" />}
              />
            </div>
          </div>
        </div>
      )}
    </StartAuthenticationHeader>
  );
};

export default PersonalInformation;
