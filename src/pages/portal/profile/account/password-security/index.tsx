import React, { useState } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import apiCore from "utils/apiCore";
import useUserHook from "hooks/useUser";
import { parseError } from "utils/error";
import { showSuccessPopupMessage, showErrorPopupMessage } from "utils/message";
import { accountPath } from "components/StartAuthentication/constant";

const PasswordInput = ({
  id,
  label,
  value,
  onChange,
  placeholder,
}: {
  id: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder: string;
}) => (
  <div className="mb-6">
    <label htmlFor={id} className="block mb-2 text-sm text-white">
      {label}
    </label>
    <div className="relative">
      <input
        id={id}
        type="password"
        value={value}
        onChange={onChange}
        className="outline-none w-full bg-transparent border border-gray-600 rounded-lg px-4 py-3 pr-12 text-white"
        required
        placeholder={placeholder}
      />
    </div>
  </div>
);

const PasswordSecurity = () => {
  const intl = useIntl();
  const router = useRouter();
  const { accessToken } = useUserHook();

  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isFormValid =
    newPassword.length >= 8 &&
    currentPassword.trim().length >= 8 &&
    newPassword === confirmPassword;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!accessToken) {
      router.push("/login");
      return;
    }

    try {
      setIsSubmitting(true);

      await apiCore.patch(
        null,
        "v1/me/password",
        {
          old_password: currentPassword,
          password: newPassword,
        },
        accessToken
      );

      showSuccessPopupMessage(
        intl.formatMessage({ id: "password_change_success" })
      );

      // Clear form
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
      router.push(accountPath);
    } catch (error) {
      console.error("Error changing password:", error);
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <StartAuthenticationHeader>
      <BackHeader
        id="account_menu_change_password"
        onBack={() => router.push(accountPath)}
      />

      <div>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Current Password */}
          <PasswordInput
            id="current-password"
            label={intl.formatMessage({ id: "current_password_label" })}
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            placeholder={intl.formatMessage({
              id: "current_password_placeholder",
            })}
          />

          {/* New Password */}
          <PasswordInput
            id="new-password"
            label={intl.formatMessage({ id: "new_password_label" })}
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder={intl.formatMessage({ id: "new_password_placeholder" })}
          />

          {/* Confirm Password */}
          <PasswordInput
            id="confirm-password"
            label={intl.formatMessage({ id: "confirm_password_label" })}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder={intl.formatMessage({
              id: "confirm_password_placeholder",
            })}
          />

          {confirmPassword && newPassword !== confirmPassword && (
            <p className="text-red-500 text-sm">
              {intl.formatMessage({ id: "password_mismatch_error" })}
            </p>
          )}

          {/* Submit Button */}
          <div className="pt-8">
            <button
              type="submit"
              disabled={!isFormValid || isSubmitting}
              className={`w-full py-3 px-4 rounded-lg font-bold text-white ${
                isFormValid && !isSubmitting
                  ? "bg-btn-gradient hover:opacity-90"
                  : "bg-gray-600 cursor-not-allowed"
              }`}
            >
              {isSubmitting
                ? intl.formatMessage({ id: "updating_password" })
                : intl.formatMessage({ id: "confirm_button" })}
            </button>
          </div>
        </form>
      </div>
    </StartAuthenticationHeader>
  );
};

export default PasswordSecurity;
