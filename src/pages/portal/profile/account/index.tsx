import React, { useMemo, useState } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import apiCore from "utils/apiCore";
import { showErrorPopupMessage, showSuccessPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import { userSignOut } from "actions/app";
import useAppSelector from "hooks/useAppSelector";
import useAppDispatch from "hooks/useAppDispatch";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import {
  personalInformationPath,
  passwordSecurityPath,
  myPreferencesPath,
  homePath,
} from "components/StartAuthentication/constant";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import DeleteConfirmModal from "components/DeleteConfirmModal";

const Account = () => {
  const intl = useIntl();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { accessToken } = useAppSelector((state) => state.app);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteAccount = async () => {
    try {
      setIsDeleting(true);
      await apiCore.delete(null, "v1/me", {}, accessToken);
      dispatch(userSignOut());
      showSuccessPopupMessage(
        intl.formatMessage({ id: "account_menu_delete_account_success" })
      );
      router.push(homePath);
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsDeleting(false);
    }
  };

  // Account menu items configuration
  const accountMenuItems = useMemo(
    () => [
      {
        id: "personal_information",
        titleKey: "account_menu_personal_information",
        href: personalInformationPath,
      },
      {
        id: "change_password",
        titleKey: "account_menu_change_password",
        href: passwordSecurityPath,
      },
      {
        id: "preferences",
        titleKey: "account_menu_preferences",
        href: myPreferencesPath,
      },
      {
        id: "privacy_policy",
        titleKey: "account_menu_privacy_policy",
        href: "/privacy",
        target: "_blank",
      },
      {
        id: "delete_account",
        titleKey: "account_menu_delete_account",
        onClick: () => setIsDeleteModalOpen(true),
      },
    ],
    []
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_account" />

      <div className="mb-8">
        <div className="space-y-6">
          <MenuItem itemList={accountMenuItems.slice(0, 3)} />
          <MenuItem itemList={accountMenuItems.slice(3)} />
        </div>
      </div>

      <DeleteConfirmModal
        isModalOpen={isDeleteModalOpen}
        setIsModalOpen={setIsDeleteModalOpen}
        title="confirm_delete_account_title"
        content="confirm_delete_account_content"
        confirmText="confirm_text"
        cancelText="cancel_text"
        onConfirm={handleDeleteAccount}
        loading={isDeleting}
      />
    </StartAuthenticationHeader>
  );
};

export default Account;
