import React, { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import Image from "next/image";
import { RootState } from "reducers";
import clsx from "clsx";

import { fetchCategoryList } from "actions/productCategory";
import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { IProductCategory } from "types/orders";
import { getLocalisedField } from "utils/locale";
import { accountPath } from "components/StartAuthentication/constant";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import AppSpin from "components/AppSpin";
import apiCore from "utils/apiCore";
import { showErrorPopupMessage, showSuccessPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import { fetchUser } from "actions/app";

const MyPreferences = () => {
  const router = useRouter();
  const { locale } = router;
  const intl = useIntl();
  const dispatch = useAppDispatch();
  const { user, accessToken } = useAppSelector((state: RootState) => state.app);
  const productCategory = useAppSelector(
    (state: RootState) => state.productCategory
  );
  const { items, isFetchItemsLoading } = productCategory;

  const [currentSelectedIdList, setCurrentSelectedIdList] = useState<number[]>(
    []
  );

  useEffect(() => {
    if (user?.user_category_ids) {
      setCurrentSelectedIdList(user.user_category_ids);
    }
  }, [user?.user_category_ids]);

  useEffect(() => {
    if (!items?.length) {
      dispatch(fetchCategoryList());
    }
  }, [dispatch, items?.length]);

  const handleSave = async () => {
    try {
      await apiCore.patch(
        null,
        "v1/me/preference",
        {
          user_category_ids: currentSelectedIdList,
        },
        accessToken
      );
      dispatch(fetchUser({ accessToken }));
      showSuccessPopupMessage(
        intl.formatMessage({ id: "account_menu_preferences_save_success" })
      );
      router.push(accountPath);
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    }
  };

  return (
    <StartAuthenticationHeader>
      <BackHeader
        id="account_menu_preferences"
        onBack={() => router.push(accountPath)}
      />
      <h2 className="text-white text-2xl font-bold">
        {intl.formatMessage({ id: "account_menu_preferences_title" })}
      </h2>

      {/* loading */}
      {(!items || items.length === 0) && isFetchItemsLoading && <AppSpin />}
      {items?.length > 0 && (
        <>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-5 mt-8">
            {items?.map((product: IProductCategory) => (
              <div
                key={product.id}
                className={clsx(
                  "cursor-pointer md:h-72 h-40 border border-gray-200 rounded-xl flex flex-col justify-center items-center",
                  currentSelectedIdList.includes(product.id) && "border-red-500"
                )}
                onClick={() => {
                  if (currentSelectedIdList.includes(product.id)) {
                    setCurrentSelectedIdList(
                      currentSelectedIdList.filter((id) => id !== product.id)
                    );
                  } else {
                    setCurrentSelectedIdList([
                      ...currentSelectedIdList,
                      product.id,
                    ]);
                  }
                }}
              >
                <div className="w-20 h-20 md:w-30 md:h-30 flex justify-center items-center">
                  <Image
                    src={product.icon_image_url}
                    width={120}
                    height={120}
                    alt={product.title}
                  />
                </div>
                <div className="font-semibold md:mt-7 mt-4 md:text-base text-[10px]">
                  {getLocalisedField(product, "title", locale)}
                </div>
              </div>
            ))}
          </div>
          <button
            onClick={handleSave}
            className={clsx(
              "my-12 w-full text-center md:text-xl bg-gradient-red font-bold md:py-3 py-2 rounded-lg"
            )}
          >
            {intl.formatMessage({ id: "account_menu_preferences_save_button" })}
          </button>
        </>
      )}
    </StartAuthenticationHeader>
  );
};

export default MyPreferences;
