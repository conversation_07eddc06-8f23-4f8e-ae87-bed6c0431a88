import React, { useMemo } from "react";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";

const AboutLegitApp = () => {
  const aboutMenuItems = useMemo(
    () => [
      {
        id: "about_us",
        titleKey: "profile_menu_about_us",
        href: "/about",
        target: "_blank",
      },
      {
        id: "how_it_works",
        titleKey: "profile_menu_how_it_works",
        href: "/app/how-it-works",
        target: "_blank",
      },
    ],
    []
  );
  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_about_legit_app" />
      <MenuItem itemList={aboutMenuItems.slice(0, 2)} />
    </StartAuthenticationHeader>
  );
};

export default AboutLegitApp;
