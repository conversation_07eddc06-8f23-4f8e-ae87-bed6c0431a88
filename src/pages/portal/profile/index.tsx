import React, { useMemo } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import Link from "next/link";

import { userSignOut } from "actions/app";
import { PATH_ROUTE } from "constants/app";
import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { showSuccessPopupMessage } from "utils/message";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import {
  aboutLegitAppPath,
  accountPath,
  bookmarksPath,
  legalInformationPath,
  orderHistoryPath,
  walletPath,
} from "components/StartAuthentication/constant";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";

const quickAccessCards = [
  {
    icon: "/profile/icon_legit_app_blog.png",
    title: "LEGIT APP Blog",
    href: "/blog",
  },
  {
    icon: "/profile/icon_search_certificate.png",
    title: "Search Certificate",
    href: "/search-certificate",
  },
  {
    icon: "/profile/icon_physical_tags.png",
    title: "Authentication Tags",
    href: "/tags",
  },
  {
    icon: "/profile/icon_nfc_chip_scan_tool.png",
    title: "NFC Chip Scan Tool",
    href: "/nfc-scan",
  },
];

const Profile = () => {
  const intl = useIntl();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.app);

  // Profile menu items configuration
  const profileMenuItems = useMemo(
    () => [
      {
        id: "account",
        icon: "/profile/icon_account.png",
        titleKey: "profile_menu_account",
        href: accountPath,
      },
      {
        id: "bookmarks",
        icon: "/profile/icon_bookmarks.png",
        titleKey: "profile_menu_bookmarks",
        href: bookmarksPath,
      },
      {
        id: "order_history",
        icon: "/profile/icon_order_history.png",
        titleKey: "profile_menu_order_history",
        href: orderHistoryPath,
      },
      {
        id: "business_inquiries",
        icon: "/profile/icon_business_inquiries.png",
        titleKey: "profile_menu_business_inquiries",
        href: "/contact",
        target: "_blank",
      },
      {
        id: "help_support",
        icon: "/profile/icon_helps_support.png",
        titleKey: "profile_menu_help_support",
        href: "/contact",
        target: "_blank",
      },
      {
        id: "legal_information",
        icon: "/profile/icon_legal_information.png",
        titleKey: "profile_menu_legal_information",
        href: legalInformationPath,
      },
      {
        id: "about_legit_app",
        icon: "/profile/icon_about_legit_app.png",
        titleKey: "profile_menu_about_legit_app",
        href: aboutLegitAppPath,
      },
    ],
    []
  );

  const handleLogout = () => {
    dispatch(userSignOut());
    showSuccessPopupMessage("You have successfully logged out.");
    router.push(PATH_ROUTE.LOGIN);
  };

  return (
    <div className="min-h-screen">
      <StartAuthenticationHeader>
        {/* User Info */}
        {user && (
          <>
            <div className="w-full p-4 rounded-md bg-gradient-purple-blue mb-4 text-white">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-full bg-white overflow-hidden border-2 border-white">
                  <img
                    src={
                      user.profile_image_url ||
                      "/profile/icon_profile_placeholder.png"
                    }
                    alt={user.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="font-bold text-xl">{user.name}</div>
                  <div className="text-sm opacity-80">{user.email}</div>
                </div>
              </div>
              <div className="space-y-2 mt-4">
                <div className="text-xs font-semibold">
                  {intl.formatMessage({
                    id: "legit_app_token_balance",
                  })}
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <img
                      src="/balance-white.svg"
                      alt="balance"
                      className="w-8 h-8"
                    />
                    <span className="text-3xl font-extrabold">
                      {Number(user.credit_balance || 0).toFixed(2)}
                    </span>
                  </div>
                  <Link
                    href={walletPath}
                    className="font-semibold px-2 sm:px-6 py-1 bg-white text-blue-200 rounded-md sm:text-lg text-sm"
                  >
                    {intl.formatMessage({
                      id: "buy",
                    })}
                  </Link>
                </div>
              </div>
            </div>
            {user.email_verified === 0 && (
              <button className="bg-gradient-purple-blue w-full py-3 rounded-md text-sm mb-4">
                {intl.formatMessage({
                  id: "verify_your_email",
                })}
              </button>
            )}
          </>
        )}

        {/* Quick Access Cards */}
        <div className="grid grid-cols-4 gap-4 mb-8">
          {quickAccessCards.map((item) => (
            <Link
              key={item.title}
              href={item.href}
              className="text-white space-y-4"
              target="_blank"
            >
              <div className="rounded-xl transition hover:scale-105 hover:shadow-lg">
                <img
                  src={item.icon}
                  alt={item.title}
                  className="w-full h-full"
                />
              </div>
              <div className="text-center sm:text-base text-[10px] font-semibold leading-tight">
                {item.title}
              </div>
            </Link>
          ))}
        </div>

        {/* Profile Menu Items */}
        <div className="my-8">
          {/* Profile Menu Items */}
          <div className="space-y-6">
            {/*  Account & Language */}
            <MenuItem itemList={profileMenuItems.slice(0, 1)} />

            {/* Bookmarks & Order History */}
            <MenuItem itemList={profileMenuItems.slice(2, 4)} />

            {/* Business, Help, Legal, About */}
            <MenuItem itemList={profileMenuItems.slice(4)} />
          </div>
        </div>

        {/* Logout */}
        <div
          onClick={handleLogout}
          className="cursor-pointer text-white text-center py-3 border border-gray-100 rounded-sm mb-6"
        >
          LOG OUT
        </div>
      </StartAuthenticationHeader>
    </div>
  );
};

export default Profile;
