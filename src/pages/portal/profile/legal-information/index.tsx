import React, { useMemo } from "react";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";

const LegalInformation = () => {
  // LegalInformation menu items configuration
  const legalInformationMenuItems = useMemo(
    () => [
      {
        id: "terms_of_use",
        titleKey: "legal_information_menu_terms_of_use",
        href: "/terms",
        target: "_blank",
      },
      {
        id: "privacy_policy",
        titleKey: "legal_information_menu_privacy_policy",
        href: "/privacy",
        target: "_blank",
      },
    ],
    []
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_legal_information" />
      <MenuItem itemList={legalInformationMenuItems} />
    </StartAuthenticationHeader>
  );
};

export default LegalInformation;
