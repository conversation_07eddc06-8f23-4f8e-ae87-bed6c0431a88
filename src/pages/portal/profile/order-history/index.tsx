import React, { useMemo } from "react";

import StartAuthenticationHeader from "components/StartAuthentication/Header";
import MenuItem from "components/StartAuthentication/Profile/MenuItem";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import {
  consumptionRecordPath,
  tokenPurchaseOrderPath,
  authTagOrderPath,
} from "components/StartAuthentication/constant";

const OrderHistory = () => {
  // Order history menu items configuration
  const orderHistoryMenuItems = useMemo(
    () => [
      {
        id: "consumption_record",
        titleKey: "order_history_menu_consumption_record",
        href: consumptionRecordPath,
      },
      {
        id: "token_purchase_order",
        titleKey: "order_history_menu_token_purchase_order",
        href: tokenPurchaseOrderPath,
      },
      {
        id: "auth_tag_order",
        titleKey: "order_history_menu_auth_tag_order",
        href: authTagOrderPath,
      },
    ],
    []
  );

  return (
    <StartAuthenticationHeader>
      <BackHeader id="profile_menu_order_history" />

      <div className="mb-8">
        <div className="space-y-6">
          <MenuItem itemList={orderHistoryMenuItems} />
        </div>
      </div>
    </StartAuthenticationHeader>
  );
};

export default OrderHistory;
