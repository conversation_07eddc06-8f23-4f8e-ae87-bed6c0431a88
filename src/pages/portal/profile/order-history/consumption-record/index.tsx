import React, { useEffect, useState, useCallback } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import apiCore from "utils/apiCore";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import useAppSelector from "hooks/useAppSelector";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import AppPlaceholder from "components/AppPlaceholder";
import AppSpin from "components/AppSpin";
import AppListLoadMoreCard from "components/AppListLoadMoreCard";
import { LIST_PAGESIZE_24 } from "constants/app";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import { ICreditBalanceLog } from "types/profile";
import { formatDate } from "utils/authHelper";
import clsx from "clsx";
import {
  BALANCE_HISTORY_TYPE,
  orderHistoryPath,
} from "components/StartAuthentication/constant";
import BalanceInfo from "components/StartAuthentication/BalanceInfo";
import { getLocalisedField } from "utils/locale";

const ConsumptionRecord = () => {
  const intl = useIntl();
  const router = useRouter();
  const { locale, defaultLocale } = router;
  const { accessToken } = useAppSelector((state) => state.app);

  const [consumptionRecord, setConsumptionRecord] = useState<
    ICreditBalanceLog[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadMoreLoading, setIsLoadMoreLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState<number>(0);

  const hasMore =
    consumptionRecord && total ? consumptionRecord.length < total : false;
  const fetchConsumptionRecord = useCallback(
    async (offset = 0, append = false) => {
      if (!accessToken) {
        showErrorPopupMessage("You need to login to view your bookmarks");
        router.push("/login");
        return;
      }

      try {
        if (append) {
          setIsLoadMoreLoading(true);
        } else {
          setIsLoading(true);
        }
        setError(null);

        const result = await apiCore.get(
          null,
          "v1/me/credit_balance_log",
          {
            $offset: offset,
            $limit: LIST_PAGESIZE_24,
          },
          accessToken
        );

        if (append) {
          setConsumptionRecord((prev) => [...prev, ...(result.data || [])]);
        } else {
          setConsumptionRecord(result.data || []);
        }
        setTotal(result.total);
      } catch (error) {
        setError(parseError(error).message);
        showErrorPopupMessage(parseError(error).message);
      } finally {
        if (append) {
          setIsLoadMoreLoading(false);
        } else {
          setIsLoading(false);
        }
      }
    },
    [accessToken, router]
  );

  const handleLoadMore = useCallback(() => {
    if (isLoadMoreLoading || !hasMore) return;

    const currentItems = consumptionRecord || [];
    fetchConsumptionRecord(currentItems.length, true);
  }, [fetchConsumptionRecord, consumptionRecord, isLoadMoreLoading, hasMore]);

  useEffect(() => {
    fetchConsumptionRecord();
  }, [fetchConsumptionRecord]);

  if (isLoading) {
    return (
      <StartAuthenticationHeader>
        <div className="flex justify-center items-center h-64">
          <AppSpin />
        </div>
      </StartAuthenticationHeader>
    );
  }

  if (error) {
    return (
      <StartAuthenticationHeader>
        <div className="flex flex-col items-center justify-center h-64">
          <div className="text-red-500 text-center">
            {intl.formatMessage({ id: "error_loading_consumption_record" })}
          </div>
        </div>
      </StartAuthenticationHeader>
    );
  }

  const getExtraServiceSnapshot = (item: ICreditBalanceLog) => {
    try {
      if (
        [
          BALANCE_HISTORY_TYPE.SERVICE_REQUEST_CREATE,
          BALANCE_HISTORY_TYPE.SERVICE_REQUEST_REFUND,
        ].includes(item.type) &&
        item.service_request?.service_extra_service_snapshot
      ) {
        const extraServiceSnapshot = JSON.parse(
          item.service_request?.service_extra_service_snapshot
        );
        return extraServiceSnapshot
          .map((item: any) => getLocalisedField(item, "title", locale))
          .join("，");
      }
      return null;
    } catch {
      return null;
    }
  };

  return (
    <StartAuthenticationHeader>
      <BackHeader
        id="order_history_menu_consumption_record_title"
        onBack={() => router.push(orderHistoryPath)}
      />
      <BalanceInfo isShowBuyBtn />

      <div className="overflow-y-auto w-full flex flex-col">
        <div className="p-4">
          {consumptionRecord.length === 0 ? (
            <AppPlaceholder
              title={intl.formatMessage({
                id: "order_history_menu_consumption_record_no_record_title",
              })}
            />
          ) : (
            <>
              <div className="divide-y divide-gray-200">
                {consumptionRecord.map((item) => (
                  <div key={item.id} className="py-4">
                    <div className="flex items-center justify-between gap-3">
                      <div>
                        <div className="text-xs sm:text-sm md:text-base">
                          {intl.formatMessage({
                            id: `key_${item.type}`,
                          })}
                          {item.service_request?.product_title && (
                            <>
                              {" - "}
                              {item.service_request?.product_title}
                            </>
                          )}
                          {getExtraServiceSnapshot(item) && (
                            <div className="text-xs sm:text-sm md:text-base">
                              {intl.formatMessage({
                                id: "authentication_service",
                              })}
                              {" - "}
                              {getExtraServiceSnapshot(item)}
                            </div>
                          )}
                        </div>
                        <div className="text-[10px] text-gray-100">
                          {formatDate(locale, defaultLocale, item.updated_at)}
                        </div>
                      </div>
                      <div
                        className={clsx(
                          "text-xs sm:text-sm md:text-base flex-shrink-0",
                          item?.credit && Number(item.credit) >= 0
                            ? "text-blue-400"
                            : "text-red-500"
                        )}
                      >
                        {item?.credit && Number(item.credit) >= 0
                          ? `+${Number(item.credit || 0).toFixed(2)} $LEGIT`
                          : `${Number(item.credit || 0).toFixed(2)} $LEGIT`}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {hasMore && (
                <div className="mt-4">
                  <AppListLoadMoreCard
                    onClick={handleLoadMore}
                    loading={isLoadMoreLoading}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </StartAuthenticationHeader>
  );
};

export default ConsumptionRecord;
