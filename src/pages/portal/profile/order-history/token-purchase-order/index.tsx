import React, { useEffect, useState, useCallback } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import apiCore from "utils/apiCore";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import useAppSelector from "hooks/useAppSelector";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import AppPlaceholder from "components/AppPlaceholder";
import AppSpin from "components/AppSpin";
import AppListLoadMoreCard from "components/AppListLoadMoreCard";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";
import { orderHistoryPath } from "components/StartAuthentication/constant";
import { LIST_PAGESIZE_24 } from "constants/app";
import { formatDate } from "utils/authHelper";
import { ITransaction } from "types/profile";

const TokenPurchaseOrder = () => {
  const intl = useIntl();
  const router = useRouter();
  const { locale, defaultLocale } = router;
  const { accessToken } = useAppSelector((state) => state.app);

  const [tokenPurchaseOrder, setTokenPurchaseOrder] = useState<ITransaction[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadMoreLoading, setIsLoadMoreLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState<number>(0);

  const hasMore =
    tokenPurchaseOrder && total ? tokenPurchaseOrder.length < total : false;
  const fetchTokenPurchaseOrder = useCallback(
    async (offset = 0, append = false) => {
      if (!accessToken) {
        showErrorPopupMessage("You need to login to view your bookmarks");
        router.push("/login");
        return;
      }

      try {
        if (append) {
          setIsLoadMoreLoading(true);
        } else {
          setIsLoading(true);
        }
        setError(null);

        const result = await apiCore.get(
          null,
          "v1/me/transaction",
          {
            $offset: offset,
            $limit: LIST_PAGESIZE_24,
          },
          accessToken
        );

        if (append) {
          setTokenPurchaseOrder((prev) => [...prev, ...(result.data || [])]);
        } else {
          setTokenPurchaseOrder(result.data || []);
        }
        setTotal(result.total);
      } catch (error) {
        setError(parseError(error).message);
        showErrorPopupMessage(parseError(error).message);
      } finally {
        if (append) {
          setIsLoadMoreLoading(false);
        } else {
          setIsLoading(false);
        }
      }
    },
    [accessToken, router]
  );

  const handleLoadMore = useCallback(() => {
    if (isLoadMoreLoading || !hasMore) return;

    const currentItems = tokenPurchaseOrder || [];
    fetchTokenPurchaseOrder(currentItems.length, true);
  }, [fetchTokenPurchaseOrder, tokenPurchaseOrder, isLoadMoreLoading, hasMore]);

  useEffect(() => {
    fetchTokenPurchaseOrder();
  }, [fetchTokenPurchaseOrder]);

  if (isLoading) {
    return (
      <StartAuthenticationHeader>
        <div className="flex justify-center items-center h-64">
          <AppSpin />
        </div>
      </StartAuthenticationHeader>
    );
  }

  if (error) {
    return (
      <StartAuthenticationHeader>
        <div className="flex flex-col items-center justify-center h-64">
          <div className="text-red-500 text-center">
            {intl.formatMessage({ id: "error_loading_token_purchase_order" })}
          </div>
        </div>
      </StartAuthenticationHeader>
    );
  }

  const renderTransactionType = (item: ITransaction) => {
    if (item.legit_tag_order) {
      switch (item.legit_tag_order.legit_tag_type) {
        case "luxe_tag":
          return "luxe_tag_purchase";
        case "kicks_tag":
          return "kicks_tag_purchase";
        default:
          return "token_plan_purchase";
      }
    }
    return "token_plan_purchase";
  };

  return (
    <StartAuthenticationHeader>
      <BackHeader
        id="order_history_menu_token_purchase_order_title"
        onBack={() => router.push(orderHistoryPath)}
      />

      <div className="overflow-y-auto w-full flex flex-col">
        <div className="p-4">
          {tokenPurchaseOrder.length === 0 ? (
            <AppPlaceholder
              title={intl.formatMessage({
                id: "order_history_menu_token_purchase_order_no_record_title",
              })}
            />
          ) : (
            <>
              <div className="divide-y divide-gray-200">
                {tokenPurchaseOrder.map((item) => (
                  <div key={item.id} className="py-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm">
                          {intl.formatMessage({
                            id: `order_history_menu_token_purchase_order_${renderTransactionType(
                              item
                            )}`,
                          })}
                        </div>
                        <div className="text-[10px] text-gray-100">
                          {formatDate(locale, defaultLocale, item.created_at)}
                        </div>
                      </div>
                      <div className="text-sm text-red-500">
                        {item.currency} ${Number(item.amount || 0).toFixed(2)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {hasMore && (
                <div className="mt-4">
                  <AppListLoadMoreCard
                    onClick={handleLoadMore}
                    loading={isLoadMoreLoading}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </StartAuthenticationHeader>
  );
};

export default TokenPurchaseOrder;
