import React, { useEffect, useState, useCallback } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";

import apiCore from "utils/apiCore";
import { showErrorPopupMessage } from "utils/message";
import { parseError } from "utils/error";
import { IBookmarkedItem } from "types/app";
import useUserHook from "hooks/useUser";
import CaseCard from "components/StartAuthentication/CaseCard";
import StartAuthenticationHeader from "components/StartAuthentication/Header";
import AppPlaceholder from "components/AppPlaceholder";
import AppSpin from "components/AppSpin";
import AppListLoadMoreCard from "components/AppListLoadMoreCard";
import { LIST_PAGESIZE_24 } from "constants/app";
import BackHeader from "components/StartAuthentication/Profile/BackHeader";

const Bookmarks = () => {
  const intl = useIntl();
  const router = useRouter();
  const { accessToken } = useUserHook();

  const [bookmarkedItems, setBookmarkedItems] = useState<IBookmarkedItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadMoreLoading, setIsLoadMoreLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState<number>(0);

  const hasMore =
    bookmarkedItems && total ? bookmarkedItems.length < total : false;
  const fetchBookmarkedItems = useCallback(
    async (offset = 0, append = false) => {
      if (!accessToken) {
        showErrorPopupMessage("You need to login to view your bookmarks");
        router.push("/login");
        return;
      }

      try {
        if (append) {
          setIsLoadMoreLoading(true);
        } else {
          setIsLoading(true);
        }
        setError(null);

        const result = await apiCore.get(
          null,
          "v1/me/bookmarked_request",
          {
            $offset: offset,
            $limit: LIST_PAGESIZE_24,
          },
          accessToken
        );

        if (append) {
          setBookmarkedItems((prev) => [...prev, ...(result.data || [])]);
        } else {
          setBookmarkedItems(result.data || []);
        }
        setTotal(result.total);
      } catch (error) {
        setError(parseError(error).message);
        showErrorPopupMessage(parseError(error).message);
      } finally {
        if (append) {
          setIsLoadMoreLoading(false);
        } else {
          setIsLoading(false);
        }
      }
    },
    [accessToken, router]
  );

  const handleLoadMore = useCallback(() => {
    if (isLoadMoreLoading || !hasMore) return;

    const currentItems = bookmarkedItems || [];
    fetchBookmarkedItems(currentItems.length, true);
  }, [fetchBookmarkedItems, bookmarkedItems, isLoadMoreLoading, hasMore]);

  useEffect(() => {
    fetchBookmarkedItems();
  }, [fetchBookmarkedItems]);

  if (isLoading) {
    return (
      <StartAuthenticationHeader>
        <div className="flex justify-center items-center h-64">
          <AppSpin />
        </div>
      </StartAuthenticationHeader>
    );
  }

  if (error) {
    return (
      <StartAuthenticationHeader>
        <div className="flex flex-col items-center justify-center h-64">
          <div className="text-red-500 text-center">
            {intl.formatMessage({ id: "error_loading_bookmarks" })}
          </div>
        </div>
      </StartAuthenticationHeader>
    );
  }

  return (
    <StartAuthenticationHeader>
      <BackHeader id="bookmarks_title" />

      <div className="overflow-y-auto w-full flex flex-col">
        <div className="p-4">
          {bookmarkedItems.length === 0 ? (
            <AppPlaceholder
              title={intl.formatMessage({ id: "no_bookmarks_title" })}
            />
          ) : (
            <>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {bookmarkedItems.map((item) => (
                  <CaseCard key={item.id} item={item} />
                ))}
              </div>

              {hasMore && (
                <div className="mt-4">
                  <AppListLoadMoreCard
                    onClick={handleLoadMore}
                    loading={isLoadMoreLoading}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </StartAuthenticationHeader>
  );
};

export default Bookmarks;
