/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import { RootState } from "reducers";

import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import NavigationHeader from "components/NavigationHeader";
import { fetchModelList, reset } from "actions/productModel";
import AppSpin from "components/AppSpin";
import { newAuthenticationPath } from "components/StartAuthentication/constant";

const ChooseModel = () => {
  const router = useRouter();
  const { id } = router.query;
  const dispatch = useAppDispatch();
  const productModel: any = useAppSelector(
    (state: RootState) => state.productModel
  );
  const { items, isFetchItemsLoading } = productModel;

  useEffect(() => {
    if (id) {
      dispatch(
        fetchModelList({
          categoryId: id?.[0],
          brandId: id?.[1],
        })
      );
    }
  }, [dispatch, id]);

  useEffect(() => {
    return () => {
      dispatch(reset());
    };
  }, []);

  return (
    <div className="text-white">
      <NavigationHeader progress="60%" />
      <div className="max-w-screen-lg m-auto md:py-10 py-4 md:px-12 px-4">
        <div className="text-2xl md:text-3xl font-bold text-center md:mb-10 mb-4">
          Choose a Model
        </div>

        {/* loading */}
        {(!items || items.length === 0) && isFetchItemsLoading && <AppSpin />}

        <div className="grid grid-cols-3 gap-5">
          {items?.map((model: any) => (
            <div
              key={model.id}
              className="py-3 md:py-6 cursor-pointer border border-gray-300 flex items-center justify-center flex-col rounded-md"
              onClick={() => {
                router.push(
                  `${newAuthenticationPath}/${id?.[0]}/${id?.[1]}/${model.id}`
                );
              }}
            >
              <div className="w-20 h-20 md:w-24 md:h-24 flex justify-center items-center">
                <Image
                  src={model.icon_image_url}
                  width={96}
                  height={96}
                  alt={model.title}
                />
              </div>
              <div className="font-semibold md:text-base text-[10px] text-center">
                {model.title}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChooseModel;
