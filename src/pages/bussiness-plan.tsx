import React, { useState } from "react";
import AppLayout from "components/AppLayout";
import AppHeader from "components/AppHeader";
import AppContainer from "components/AppContainer";
import Image from "next/image";

interface PlanItem {
  name: string;
  price: string;
  oldPrice: string;
  unit: string;
}

interface PlanSection {
  title: string;
  desc: string;
  highlight: string;
  highlightColor: string;
  subHighlight?: string;
  subHighlightColor?: string;
  extra?: string;
  extraColor?: string;
  plans: PlanItem[];
}

type TabKey = "tokens" | "luxury" | "sneakers";

const TABS: { key: TabKey; label: string }[] = [
  { key: "tokens", label: "Tokens" },
  { key: "luxury", label: "Luxury Products" },
  { key: "sneakers", label: "Sneakers" },
];

const plansData: Record<TabKey, PlanSection[]> = {
  tokens: [
    {
      title: "Business Token Plans",
      desc: "Provide the bulk purchasing for business use",
      highlight: "Better Discounts",
      highlightColor: "text-green-400",
      plans: [
        {
          name: "100 $LEGIT Tokens + Extra 10",
          price: "100.00",
          oldPrice: "110.00",
          unit: "USD",
        },
        {
          name: "250 $LEGIT Tokens + Extra 35",
          price: "250.00",
          oldPrice: "285.00",
          unit: "USD",
        },
        {
          name: "500 $LEGIT Tokens + Extra 100",
          price: "500.00",
          oldPrice: "600.00",
          unit: "USD",
        },
        {
          name: "1000 $LEGIT Tokens + Extra 250",
          price: "1,000.00",
          oldPrice: "1,250.00",
          unit: "USD",
        },
      ],
    },
  ],
  luxury: [
    {
      title: "LUXE TAG Plans",
      desc: "Designed for luxury authentication users. Each tag includes 10 $LEGIT Tokens for authentication.",
      highlight: "Physical Tag Authentication",
      highlightColor: "text-green-400",
      subHighlight: "Merchant Verification",
      subHighlightColor: "text-yellow-400",
      extra: "FREE Worldwide Shipping",
      extraColor: "text-blue-300",
      plans: [
        {
          name: "25 LUXE TAGS",
          price: "280.00",
          oldPrice: "500.00",
          unit: "USD",
        },
      ],
    },
  ],
  sneakers: [
    {
      title: "KICKS TAG Plans",
      desc: "Designed for sneaker authentication users. Each tag includes 3 $LEGIT Tokens for authentication.",
      highlight: "Physical Tag Authentication",
      highlightColor: "text-green-400",
      subHighlight: "Merchant Verification",
      subHighlightColor: "text-yellow-400",
      extra: "FREE Worldwide Shipping",
      extraColor: "text-blue-300",
      plans: [
        {
          name: "35 KICKS TAGS",
          price: "120.00",
          oldPrice: "420.00",
          unit: "USD",
        },
        {
          name: "100 KICKS TAGS",
          price: "350.00",
          oldPrice: "1,200.00",
          unit: "USD",
        },
        {
          name: "200 KICKS TAGS",
          price: "600.00",
          oldPrice: "2,400.00",
          unit: "USD",
        },
        {
          name: "500 KICKS TAGS",
          price: "1,280.00",
          oldPrice: "6,000.00",
          unit: "USD",
        },
      ],
    },
  ],
};

const EnterpriseSection = () => (
  <div className="bg-yellow-900/80 rounded-2xl p-6 mt-8 text-white">
    <div className="text-2xl font-bold mb-2">
      Contact Our Sales Team for Enterprise Benefits
    </div>
    <div className="mb-4 text-gray-100 text-base">
      Verify your company information with LEGIT APP, enjoy enterprise exclusive
      benefits and get your LEGIT APP Verified badge.
    </div>
    <div className="flex flex-col gap-3">
      <div className="flex items-center gap-2">
        <span className="inline-block w-6 h-6 bg-blue-900 rounded-full flex items-center justify-center">
          <Image
            src="/icon_token_discount.png"
            alt="token discount"
            width={20}
            height={20}
          />
        </span>
        <span className="font-bold">Exclusive Token Discounts</span>
        <span className="text-gray-100">
          Enjoy exclusive discount for enterprise partners on token purchases of
          $500 or more.
        </span>
      </div>
      <div className="flex items-center gap-2">
        <span className="inline-block w-6 h-6 bg-blue-900 rounded-full flex items-center justify-center">
          <Image
            src="/icon_financial_guarantee.png"
            alt="guarantee"
            width={20}
            height={20}
          />
        </span>
        <span className="font-bold">Enhanced Financial Guarantee</span>
        <span className="text-gray-100">
          100% Money-Back Protection from our 90-day Financial Guarantee for all
          authentication done by Legit App.
        </span>
      </div>
      <div className="flex items-center gap-2">
        <span className="inline-block w-6 h-6 bg-blue-900 rounded-full flex items-center justify-center">
          <Image
            src="/icon_tag_support.png"
            alt="tag support"
            width={20}
            height={20}
          />
        </span>
        <span className="font-bold">FREE Physical Tag Support</span>
        <span className="text-gray-100">
          Get FREE Authentication Tags for your business.
        </span>
      </div>
    </div>
  </div>
);

const BussinessPlanPage = () => {
  const [tab, setTab] = useState<TabKey>("sneakers");
  return (
    <AppLayout>
      <AppHeader />
      <AppContainer>
        <div className="max-w-2xl mx-auto mt-8">
          <div className="flex justify-center gap-8 border-b border-gray-700 mb-8">
            {TABS.map((t) => (
              <button
                key={t.key}
                className={`pb-2 px-2 text-lg font-semibold transition-colors ${
                  tab === t.key
                    ? "text-white border-b-2 border-red-500"
                    : "text-gray-400"
                }`}
                onClick={() => setTab(t.key)}
              >
                {t.label}
              </button>
            ))}
          </div>
          {plansData[tab].map((section: PlanSection, idx: number) => (
            <div
              key={idx}
              className="bg-dark-400 rounded-2xl p-6 mb-8 text-white relative overflow-hidden"
            >
              {/* 可根据 tab 显示不同图片 */}
              {tab === "sneakers" && (
                <Image
                  src="/sneaker_plan.png"
                  alt="Sneaker"
                  width={120}
                  height={80}
                  className="absolute right-6 top-6"
                />
              )}
              {tab === "luxury" && (
                <Image
                  src="/luxury_plan.png"
                  alt="Luxury"
                  width={120}
                  height={80}
                  className="absolute right-6 top-6"
                />
              )}
              <div className="text-2xl font-bold mb-2">{section.title}</div>
              <div className="text-gray-100 mb-4">{section.desc}</div>
              <div className="flex items-center gap-4 mb-4">
                <span className={`font-semibold ${section.highlightColor}`}>
                  {section.highlight}
                </span>
                {section.subHighlight && (
                  <span
                    className={`font-semibold ${section.subHighlightColor}`}
                  >
                    {section.subHighlight}
                  </span>
                )}
                {section.extra && (
                  <span className={`font-semibold ${section.extraColor}`}>
                    {section.extra}
                  </span>
                )}
              </div>
              <div className="my-8 h-px bg-gradient-to-r from-gray-100 via-gray-600 to-transparent" />
              <div className="space-y-4">
                {section.plans.map((plan: PlanItem, i: number) => (
                  <div
                    key={i}
                    className="flex justify-between items-center bg-dark-300 rounded-xl px-6 py-4"
                  >
                    <div>
                      <div className="font-bold text-lg">{plan.name}</div>
                      <div className="text-gray-400 line-through text-sm">
                        {plan.oldPrice} {plan.unit}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="font-bold text-xl">
                        {plan.price} {plan.unit}
                      </div>
                      <button className="bg-transparent border border-white text-white font-medium py-2 px-6 rounded-lg">
                        BUY
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
          <EnterpriseSection />
        </div>
      </AppContainer>
    </AppLayout>
  );
};

export default BussinessPlanPage;
