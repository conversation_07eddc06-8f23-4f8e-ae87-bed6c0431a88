import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import React from "react";
import { IAppBanner } from "types/app";
import { getLocalisedField } from "utils/locale";

const Banner = ({ bannerList }: { bannerList: IAppBanner[] }) => {
  const router = useRouter();
  const { locale } = router;
  return (
    <div className="flex gap-4 overflow-x-auto whitespace-nowrap hide-scrollbar rounded-md w-full">
      {bannerList.map((banner) => (
        <Link
          key={banner.id}
          className="flex-shrink-0 md:w-[32%] w-[50%] aspect-[362/164] cursor-pointer"
          href={banner.redirect_url}
          target="_blank"
        >
          <Image
            src={getLocalisedField(banner, "cover_image_url", locale)}
            alt={banner.title}
            width={411}
            height={231}
            className="object-container rounded-md overflow-hidden w-full"
          />
        </Link>
      ))}
    </div>
  );
};

export default Banner;
