import { RightOutlined } from "@ant-design/icons";
import Link from "next/link";
import React from "react";
import { IAppArticle } from "types/app";

const Article = ({ articleList }: { articleList: IAppArticle[] }) => {
  return (
    <Link
      href="/blog"
      className="w-full h-10 md:h-16 bg-purple-pink-gradient flex items-center md:px-7 px-4 rounded-lg justify-between cursor-pointer"
    >
      <div className="max-w-[85%] truncate md:text-base text-xs">
        {articleList[0]?.title}
      </div>
      <div>
        <RightOutlined />
      </div>
    </Link>
  );
};

export default Article;
