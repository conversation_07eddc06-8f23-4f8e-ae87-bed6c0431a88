import React, { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import Link from "next/link";
import { RootState } from "reducers";
import clsx from "clsx";

import useAppSelector from "hooks/useAppSelector";
import useAppDispatch from "hooks/useAppDispatch";
import useUserHook from "hooks/useUser";
import { fetchCreditPlan } from "actions/creditPlan";
import { fetchCurrencyRates } from "actions/currencyRate";
import { ICredit } from "types/wallet";
import { getLocalisedField } from "utils/locale";
import BalanceInfo from "components/StartAuthentication/BalanceInfo";
import AppSpin from "components/AppSpin";
import AppModal from "components/AppModal";
import CurrencyRateCard from "components/StartAuthentication/WalletComp/CurrencyRateCard";
import { ICurrencyRate } from "types/app";
import Ads from "components/StartAuthentication/WalletComp/Ads";
import { orderDetailPath } from "../constant";
import { showErrorPopupMessage } from "utils/message";

const WalletComp = ({ tokenBgc = "" }: { tokenBgc?: string }) => {
  const router = useRouter();
  const { locale } = router;
  const dispatch = useAppDispatch();
  const creditPlanState = useAppSelector(
    (state: RootState) => state.creditPlan
  );
  const currencyRate = useAppSelector((state: RootState) => state.currencyRate);

  const { accessToken } = useUserHook();

  const { items, isFetchItemsLoading, currency_rate = {} } = creditPlanState;
  const { items: currencyRateList } = currencyRate;

  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [currentSelectedCurrency, setCurrentSelectedCurrency] =
    React.useState<ICurrencyRate | null>(null);

  useEffect(() => {
    if (!items) {
      dispatch(fetchCreditPlan());
    }
  }, [items, dispatch]);

  useEffect(() => {
    if (!currencyRateList) {
      dispatch(fetchCurrencyRates());
      return;
    }
    setCurrentSelectedCurrency(currencyRateList[0]);
  }, [currencyRateList, dispatch]);
  return (
    <>
      <div>
        <div className="flex justify-between items-center mb-5">
          <div className="text-white font-bold md:text-[32px] sm:text-2xl text-xl">
            My Wallet
          </div>
          <Link
            href="/app/legit-token"
            className="flex gap-1 items-center cursor-pointer"
            target="_blank"
          >
            <Image
              src="/help.svg"
              width={20}
              height={20}
              alt="help"
              className="w-3 h-3 md:w-5 md:h-5"
            />
            <div className="text-gray-100 md:text-base sm:text-sm text-xs">
              What are $LEGIT
            </div>
          </Link>
        </div>
        {/* balance info */}
        <BalanceInfo />

        {/* token plans */}
        <div className="text-white">
          {/* header */}
          <div className="flex justify-between items-center my-3">
            <div className="md:text-2xl font-bold text-xl">Buy Token Plans</div>
            <div
              className="cursor-pointer bg-gray-100 text-dark-100 rounded-full py-1 px-2 font-extrabold md:text-base text-sm"
              onClick={() => setIsModalOpen(true)}
            >
              {currentSelectedCurrency?.currency_code || "USD"}
            </div>
          </div>
          {isFetchItemsLoading ? (
            <AppSpin />
          ) : (
            <>
              <div className="grid grid-cols-2 gap-4">
                {items
                  ?.filter((credit: ICredit) => !credit.business)
                  ?.map((credit: ICredit) => (
                    <div
                      key={credit.id}
                      className={clsx(
                        "flex items-center md:gap-4 gap-1 rounded-lg md:px-8 md:py-9 px-4 py-2 cursor-pointer",
                        tokenBgc ? tokenBgc : "bg-dark-100"
                      )}
                      onClick={() => {
                        if (!accessToken) {
                          showErrorPopupMessage("Please login first");
                          return;
                        }
                        router.push(
                          `${orderDetailPath}/${credit.id}?callback=${
                            router.asPath.split("?")[0]
                          }`
                        );
                      }}
                    >
                      <div className="md:w-12 md:h-12 w-8 h-8">
                        <Image
                          src={credit.logo_image_url || "/balance-gold.svg"}
                          width={48}
                          height={48}
                          alt={credit.subtitle}
                        />
                      </div>
                      <div className="flex flex-col gap-1">
                        <div className="md:text-xl text-sm font-black">
                          {getLocalisedField(credit, "subtitle", locale)}
                        </div>
                        <div className="md:text-xl text-sm font-black flex gap-2">
                          <div>
                            {(
                              Number(credit.price || 1) *
                                currency_rate[
                                  currentSelectedCurrency?.currency_code ||
                                    "USD"
                                ] || 1
                            ).toFixed(2)}
                          </div>
                          <div>
                            {currentSelectedCurrency?.currency_code || "USD"}
                          </div>
                        </div>
                        {currentSelectedCurrency &&
                          currentSelectedCurrency?.currency_code !== "USD" && (
                            <div className="text-xs text-blue-200">
                              ({Number(credit.price || 1).toFixed(2)} USD)
                            </div>
                          )}
                      </div>
                    </div>
                  ))}
              </div>
              {/* ads */}
              <Ads />
            </>
          )}
        </div>
      </div>
      <AppModal
        title="Currency Converter"
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
      >
        {currencyRateList?.map((currency: ICurrencyRate) => (
          <CurrencyRateCard
            key={currency.id}
            currency={currency}
            locale={locale || ""}
            onSelected={(selectedCurrency: ICurrencyRate) =>
              setCurrentSelectedCurrency(selectedCurrency)
            }
            currentSelectedCurrency={currentSelectedCurrency}
          />
        ))}
      </AppModal>
    </>
  );
};

export default WalletComp;
