import React from "react";
import { useRouter } from "next/router";
import { IServiceRequestResult } from "types/orders";
import { formatDate } from "utils/authHelper";
import { authenticatorProfilePath } from "../constant";

const ReviewsFromAuthenticators = ({
  locale,
  defaultLocale,
  serviceRequestResult,
}: {
  locale: string;
  defaultLocale: string;
  serviceRequestResult?: IServiceRequestResult[];
}) => {
  const router = useRouter();
  return (
    <div>
      {!!serviceRequestResult?.length && (
        <div className="space-y-4">
          {serviceRequestResult.map((result) => (
            <div
              key={result.id}
              className="bg-dark-100 rounded-md p-4 space-y-2"
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full overflow-hidden">
                    <img
                      src={
                        result.checker.profile_image_url ||
                        "/order/icon_detail_result_overlay.png"
                      }
                      className="w-full h-full"
                      alt="Authenticator icon"
                    />
                  </div>
                  <div>
                    <div className="text-sm">
                      {result?.checker?.name} (#{result?.checker_id})
                    </div>
                    <div className="text-xs text-gray-100">
                      {formatDate(locale, defaultLocale, result?.created_at)}
                    </div>
                  </div>
                </div>
                <button
                  className="md:text-sm text-xs bg-gray-700 rounded-md p-2 px-3 hover:bg-gray-600 cursor-pointer transition-colors"
                  onClick={() =>
                    router.push(
                      `${authenticatorProfilePath}/${result?.checker_id}`
                    )
                  }
                >
                  Profile
                </button>
              </div>
              <div className="text-sm whitespace-pre-line">
                {result.checker_remark}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ReviewsFromAuthenticators;
