import React from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import clsx from "clsx";

import {
  getAuthStatus,
  formatDate,
  getAuthResultDesc,
  getResultTextColor,
} from "utils/authHelper";
import { RESULT_TYPE } from "components/StartAuthentication/constant";

interface MatchedResultsProps {
  result?: "pass" | "not_pass";
  categoryId?: number;
  uuid?: string;
  completedAt?: string;
  user_remark?: string | null;
}

const MatchedResults = ({
  result,
  categoryId,
  uuid,
  completedAt,
}: MatchedResultsProps) => {
  const intl = useIntl();
  const router = useRouter();
  const { locale = "", defaultLocale = "" } = router;
  return (
    <div className="flex flex-col items-center justify-center gap-2">
      {/* logo */}
      <div className="w-8 md:w-10 mb-3">
        <img
          src={
            result === RESULT_TYPE.PASS
              ? "/order/icon_authentic.png"
              : "/order/icon_replica.png"
          }
          className="w-full h-auto object-contain"
          alt="Authentication logo"
        />
      </div>

      {/* Result Status */}
      <div className={clsx("font-bold", getResultTextColor(result || ""))}>
        {intl.formatMessage({ id: getAuthStatus({ result, categoryId }) })}
      </div>

      {/* Order Details */}
      {uuid && <div className="text-gray-100 text-xs sm:text-sm ">#{uuid}</div>}

      {/* Completion Date */}
      {completedAt && (
        <div className="text-gray-100 text-xs sm:text-sm">
          Completed at: {formatDate(locale, defaultLocale, completedAt)}
        </div>
      )}

      {/* Authentication Description */}
      <div className="bg-dark-100 rounded-lg md:p-4 p-2 md:max-w-md mx-auto">
        <div className="text-gray-100 text-xs sm:text-sm ">
          {intl.formatMessage({
            id: getAuthResultDesc({ categoryId, result }),
          })}
        </div>
      </div>
    </div>
  );
};

export default MatchedResults;
