import React, { useEffect } from "react";
import HeaderTitle from "./HeaderTitle";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";

const CertificateOwner = ({ ownerName }: { ownerName: string }) => {
  const { certificateOwnerName, setCertificateOwnerName } =
    useNewAuthenticationForm();

  useEffect(() => {
    setCertificateOwnerName(ownerName);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ownerName]);
  return (
    <div>
      {/* title */}
      <HeaderTitle title="Certificate Owner" optional={true} />

      <div className="flex flex-col md:gap-5 gap-3">
        {/* desc */}
        <div className="text-gray-500 md:text-base text-xs">
          Please enter your legal name or legal business entity name to be
          eligible for the Financial Guarantee policy.
        </div>

        {/* input */}
        <input
          placeholder="Owner Name to be Shown on Certificate"
          className="md:text-base text-xs outline-none bg-transparent border-gray-200 border w-full rounded-md px-2 py-2 placeholder-gray-100"
          value={certificateOwnerName}
          onChange={(e) => setCertificateOwnerName(e.target.value)}
        />
        <div className="md:text-base text-xs w-full text-center text-gray-500 cursor-pointer">
          View Certificate Example:
          <a
            className="underline mx-2"
            href="https://legitapp.com/zh-Hant/cert/3066123689299189"
            target="_blank"
          >
            [Authentic]
          </a>
          <a
            className="underline"
            href="https://legitapp.com/zh-Hant/cert/3408395499395160"
            target="_blank"
          >
            [Replica]
          </a>
        </div>
      </div>
    </div>
  );
};

export default CertificateOwner;
