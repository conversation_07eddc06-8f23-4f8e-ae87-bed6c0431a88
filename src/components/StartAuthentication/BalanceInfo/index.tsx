import React, { useEffect, useState } from "react";
import Image from "next/image";

import useUserHook from "hooks/useUser";
import AlmostThere from "components/AlmostThere";
import WalletComp from "components/StartAuthentication/WalletComp";
import AppModal from "components/AppModal";

const BalanceInfo = ({ isShowBuyBtn = false }: { isShowBuyBtn?: boolean }) => {
  const { user, refetchUser } = useUserHook();

  useEffect(() => {
    refetchUser();
  }, []);

  const [isModalOpen, setIsModalOpen] = useState(false);

  return user ? (
    <>
      <div className="w-full md:p-8 p-4 bg-balance-gradient rounded-md md:rounded-xl flex flex-col md:gap-4 gap-2 text-white">
        <div className="font-bold sm:text-base text-sm">
          $LEGIT TOKEN BALANCE
        </div>
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Image
              src="/balance-white.svg"
              width={44}
              height={44}
              alt="balance"
              className="md:w-11 md:h-11 w-6 h-6"
            />
            <div className="md:text-3xl font-extrabold text-2xl">
              {Number(user.credit_balance || 0).toFixed(2)}
            </div>
          </div>
          {isShowBuyBtn && (
            <div
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              <div className="cursor-pointer bg-white text-purple-100 md:py-2 md:px-4 px-2 py-1 md:text-xl font-extrabold rounded-lg">
                BUY
              </div>
            </div>
          )}
        </div>
      </div>
      <AppModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        title=""
      >
        <WalletComp tokenBgc="bg-gray-200" />
      </AppModal>
    </>
  ) : (
    <AlmostThere desc1="checking your balance" desc2="check your balance" />
  );
};

export default BalanceInfo;
