import React, { useEffect, useCallback } from "react";
import { useIntl } from "react-intl";
import { useRouter } from "next/router";
import { RootState } from "reducers";

import useAppDispatch from "hooks/useAppDispatch";
import useAppSelector from "hooks/useAppSelector";
import { fetchItems } from "actions/caseHistory";
import { LIST_PAGESIZE_24 } from "constants/app";
import { ICaseHistory } from "types/app";
import AppListLoadMoreCard from "components/AppListLoadMoreCard";
import AppPlaceholder from "components/AppPlaceholder";
import { getErrorMessage } from "utils/error";
import CaseCard from "components/StartAuthentication/CaseCard";

const CaseHistory = () => {
  const intl = useIntl();
  const dispatch = useAppDispatch();

  const caseHistoryState = useAppSelector(
    (state: RootState) => state.caseHistory
  );

  const {
    items,
    isFetchItemsLoading,
    pagination = {},
    fetchItemsErrors,
  } = caseHistoryState || {};

  const { total } = pagination || {};
  const hasMore = items && total ? items.length < total : false;

  const handleLoadMore = useCallback(() => {
    if (isFetchItemsLoading || !hasMore) return;

    const currentItems = items || [];
    dispatch(
      fetchItems(
        {
          $offset: currentItems.length,
          $limit: LIST_PAGESIZE_24,
        },
        items
      )
    );
  }, [dispatch, items, isFetchItemsLoading, hasMore]);

  useEffect(() => {
    if (!items) {
      dispatch(
        fetchItems({
          $limit: LIST_PAGESIZE_24,
        })
      );
    }
  }, [dispatch, items]);

  return (
    <div className="flex flex-col gap-4">
      {/* header */}
      <div className="flex justify-between items-center">
        <div className="font-bold md:text-2xl text-xl">
          {intl.formatMessage({ id: "start_authentication_page_case_history" })}
        </div>
        <div className="text-gray-100 bg-gray-300 rounded-lg md:px-4 md:py-2 py-1 px-2 cursor-pointer md:text-base text-sm">
          {intl.formatMessage({
            id: "start_authentication_page_by_categories",
          })}
        </div>
      </div>
      {/* content */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {items &&
          items.map((item: ICaseHistory) => (
            <CaseCard key={item.id} item={item} />
          ))}
      </div>
      {fetchItemsErrors && fetchItemsErrors.length > 0 && (
        <div>
          <AppPlaceholder
            iconType="exclamation-circle"
            title={getErrorMessage(fetchItemsErrors)}
          />
        </div>
      )}
      {items && items.length < total && (
        <div>
          <AppListLoadMoreCard
            onClick={handleLoadMore}
            loading={isFetchItemsLoading}
          />
        </div>
      )}
      {isFetchItemsLoading && !items && (
        <div className="flex flex-col items-center justify-center py-4 gap-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-100"></div>
          <div>loading...</div>
        </div>
      )}
    </div>
  );
};

export default CaseHistory;
