import { useRouter } from "next/router";
import { LeftOutlined } from "@ant-design/icons";
import { useIntl } from "react-intl";
import { profilePath } from "components/StartAuthentication/constant";
import clsx from "clsx";

const BackHeader = ({
  id,
  onBack,
  rightContent,
  className = "",
}: {
  id?: string;
  onBack?: () => void;
  rightContent?: React.ReactNode;
  className?: string;
}) => {
  const intl = useIntl();
  const router = useRouter();

  return (
    <div className={clsx("flex items-center justify-between mb-8", className)}>
      <div
        className="cursor-pointer"
        onClick={() => (onBack ? onBack() : router.push(profilePath))}
      >
        <LeftOutlined style={{ fontSize: "24px" }} />
      </div>
      {id && (
        <div className="text-white font-bold sm:text-2xl text-xl">
          {intl.formatMessage({ id })}
        </div>
      )}
      {rightContent ? rightContent : <div></div>}
    </div>
  );
};

export default BackHeader;
