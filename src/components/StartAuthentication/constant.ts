export const authenticationBasePath = "/portal";

export const homePath = `${authenticationBasePath}`;
export const walletPath = `${authenticationBasePath}/wallet`;
export const ordersPath = `${authenticationBasePath}/orders`;
export const profilePath = `${authenticationBasePath}/profile`;

export const chooseCategoryPath = `${authenticationBasePath}/choose-category`;
export const chooseBrandPath = `${authenticationBasePath}/choose-brand`;
export const chooseModalPath = `${authenticationBasePath}/choose-model`;
export const newAuthenticationPath = `${authenticationBasePath}/new-authentication`;
export const checkoutPath = `${authenticationBasePath}/checkout`;
export const orderDetailPath = `${walletPath}/buy-token-plan`;
export const authenticatorProfilePath = `${authenticationBasePath}/authenticator`;
export const accountPath = `${profilePath}/account`;
export const bookmarksPath = `${profilePath}/bookmarks`;
export const orderHistoryPath = `${profilePath}/order-history`;
export const legalInformationPath = `${profilePath}/legal-information`;
export const aboutLegitAppPath = `${profilePath}/about-legit-app`;

// Account sub-menu paths
export const personalInformationPath = `${accountPath}/personal-information`;
export const passwordSecurityPath = `${accountPath}/password-security`;
export const myPreferencesPath = `${accountPath}/my-preferences`;

// Order history sub-menu paths
export const consumptionRecordPath = `${orderHistoryPath}/consumption-record`;
export const tokenPurchaseOrderPath = `${orderHistoryPath}/token-purchase-order`;
export const authTagOrderPath = `${orderHistoryPath}/auth-tag-order`;

export const caseDetailPath = `${authenticationBasePath}/case-detail`;
export const ChooseCategoryByBrandIdPath = `${authenticationBasePath}/choose-category`;

export const getOrdersConversationPath = (id: string) =>
  `${ordersPath}/${id}/conversation`;

export const menuItems = [
  {
    title: "Home",
    href: homePath,
    icon: "/menu/home.svg",
  },
  {
    title: "Wallet",
    href: walletPath,
    icon: "/menu/wallet.svg",
  },
  {
    title: "Orders",
    href: ordersPath,
    icon: "/menu/orders.svg",
  },
  {
    title: "Profile",
    href: profilePath,
    icon: "/menu/profile.svg",
  },
];

export const DISCOUNT_TYPE = {
  ABSOLUTE: "absolute",
  RELATIVE: "relative",
};

export const ORDER_STATUS = {
  IN_PROGRESS: "checker_pending",
  COMPLETED: "completed",
  CANCELLED: "cancelled",
  USER_PENDING: "user_pending",
};

export const RISK_LEVELS = {
  VERY_LOW: "AI_SCORE_Very_Low_Risk",
  LOW: "AI_SCORE_Low_Risk",
  MODERATE: "AI_SCORE_Moderate_Risk",
  ELEVATED: "AI_SCORE_Elevated_Risk",
  HIGH: "AI_SCORE_High_Risk",
  VERY_HIGH: "AI_SCORE_Very_High_Risk",
  SIGNIFICANT: "AI_SCORE_Significant_Risk",
  MAJOR: "AI_SCORE_Major_Risk",
  SEVERE: "AI_SCORE_Severe_Risk",
  CRITICAL: "AI_SCORE_Critical_Risk",
  EXTREME: "AI_SCORE_Extreme_Risk",
};

export const RESULT_TYPE = {
  PASS: "pass",
  NOT_PASS: "not_pass",
  UNABLE_TO_VERIFY: "unable_to_verify",
};

export const SERVICE_REQUEST_IMAGE_TYPE = {
  INITIAL: "initial",
  ADDITIONAL: "additional",
};

export const SERVICE_REQUEST_ADDITIONAL_STATUS = {
  PENDING: "pending",
  COMPLETED: "completed",
  CANCELLED: "cancelled",
};

export const CATEGORY = {
  CODE_CHECKING: 8,
  SNEAKER_CATEGORY_ID: 1,
};

export const CONVERSATION_FORMAT_TYPE = {
  IMAGE: ["image/jpeg", "image/jpg", "image/png", "image/webp", "image/*"],
};

export const BALANCE_HISTORY_TYPE = {
  CREDIT_PLAN_PURCHASE: "credit_plan_purchase",
  CREDIT_PLAN_GIFT_CARD: "credit_plan_gift_card",
  EMAIL_VERIFICATION_REWARD: "email_verification_reward",
  LOTTERY_TICKET_PURCHASE: "lottery_ticket_purchase",
  LOTTERY_PRIZE: "lottery_prize",
  MARKETPLACE_NEW_LISTING: "marketplace_new_listing",
  MARKETPLACE_CANCEL_REFUND: "marketplace_cancel_refund",
  REFERRER_COMMISSION: "referrer_commission",
  REFERREE_COMMISSION: "referree_commission",
  REFERRER_REGISTRATION_REWARD: "referrer_registration_reward",
  REFEREE_REGISTRATION_REWARD: "referee_registration_reward",
  SERVICE_REQUEST_CREATE: "service_request_create",
  SERVICE_REQUEST_REFUND: "service_request_refund",
  SERVICE_REQUEST_COMPARISON_REPORT_CREATE:
    "service_request_comparison_report_create",
  TASK_COMPLETION_REWARD: "task_completion_reward",
  SERVICE_EXTRA_SERVICE_INSURANCE_REFUND:
    "service_extra_service_insurance_refund",
  SERVICE_EXTRA_SERVICE_NFI_CERTIFICATE_REFUND:
    "service_extra_service_nfi_certificate_refund",
  SERVICE_EXTRA_SERVICE_REFUND_CODE_CHECKING:
    "service_extra_service_refund_code_checking",
  LEGIT_TAG_ORDER: "legit_tag_order",
  LEGIT_TAG_REFUND: "legit_tag_refund",
  OTHER: "other",
};
