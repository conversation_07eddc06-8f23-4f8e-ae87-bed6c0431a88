import React from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import Link from "next/link";

import { IBookmarkedItem, ICaseHistory } from "types/app";
import { formatDate } from "utils/authHelper";
import resizeImageUrl from "utils/resizeImageUrl";
import getImageUrl from "utils/imageUrl";
import AuthOrReplicaCard from "components/StartAuthentication/AuthOrReplicaCard";
import { caseDetailPath } from "components/StartAuthentication/constant";

const CaseCard = ({ item }: { item: ICaseHistory | IBookmarkedItem }) => {
  const router = useRouter();
  const { locale, defaultLocale } = router;
  return (
    <Link
      key={item.id}
      className="border mb-4 md:rounded-xl rounded-md border-gray-200 overflow-hidden relative cursor-pointer"
      href={`${caseDetailPath}/${item.uuid}`}
    >
      <div className="w-full aspect-square">
        <Image
          className="w-full h-full object-cover"
          alt={item.product_title}
          src={resizeImageUrl(getImageUrl(item.cover_image_url), {
            width: 300,
          })}
          width={366}
          height={366}
        />
      </div>
      <div className="flex items-center md:my-3 my-2 md:gap-4 gap-2 md:px-2 px-2">
        <div className="md:w-14 md:h-14 w-10 h-10">
          <Image
            alt={item.product_brand?.icon_image_url}
            src={item.product_brand?.icon_image_url}
            width={64}
            height={64}
          />
        </div>
        <div className="flex flex-col gap-0.5">
          <div className="text-[10px] font-medium">
            {item.product_title.split("-")[0]}
          </div>
          <div className="font-bold text-[10px]">
            {item.product_title.split("-")[1]}
          </div>
          <div className="text-gray-100 text-[10px]">
            {formatDate(locale, defaultLocale, item.completed_at)}
          </div>
        </div>
      </div>
      <div className="absolute sm:top-3 sm:left-3 top-2 left-2">
        <AuthOrReplicaCard result={item.result} />
      </div>
    </Link>
  );
};

export default CaseCard;
