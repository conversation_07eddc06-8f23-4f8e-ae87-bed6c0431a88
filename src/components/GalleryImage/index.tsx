import clsx from "clsx";
import React, { useState } from "react";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";

const GalleryImage = ({
  imageList,
  isShowMarker = true,
  className,
  isShowLightbox = true,
}: {
  imageList: any[];
  isShowMarker?: boolean;
  className?: string;
  isShowLightbox?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const slides = imageList.map((image) => ({
    src: image.image_url,
    width: image.width,
    height: image.height,
  }));

  const handleImageClick = (index: number) => {
    if (isShowLightbox) {
      setCurrentIndex(index);
      setIsOpen(true);
    }
  };

  return (
    <>
      <div className={clsx("grid grid-cols-4 gap-2", className ?? "")}>
        {imageList.map((image, index) => (
          <div
            key={image.id}
            onClick={() => handleImageClick(index)}
            className="relative aspect-square cursor-pointer hover:opacity-90 transition-opacity"
          >
            <img
              src={image.image_url}
              className="w-full h-full rounded-lg object-cover"
              alt=""
            />
            {isShowMarker && (
              <img
                src="/OX.svg"
                alt="OX icon"
                className="absolute inset-0 object-contain w-full h-full"
              />
            )}
          </div>
        ))}
      </div>

      <Lightbox
        open={isOpen}
        close={() => setIsOpen(false)}
        slides={slides}
        index={currentIndex}
        render={{
          slide: ({ slide }) => (
            <img
              src={slide.src}
              alt=""
              style={{
                maxWidth: "100%",
                maxHeight: "100%",
                objectFit: "contain",
                width: "auto",
                height: "auto",
              }}
            />
          ),
        }}
        carousel={{
          finite: imageList.length <= 1,
        }}
        controller={{
          closeOnBackdropClick: true,
          closeOnPullDown: true,
          closeOnPullUp: true,
        }}
      />
    </>
  );
};

export default GalleryImage;
