import React from "react";
import Image from "next/image";
import { useRouter } from "next/router";

import AppModal from "components/AppModal";

interface PaymentSuccessModalProps {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  tokenName: string;
  tokenId: string;
  amount: string;
}

const PaymentSuccessModal: React.FC<PaymentSuccessModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  tokenName,
  tokenId,
  amount,
}) => {
  const router = useRouter();
  return (
    <AppModal
      title=""
      isModalOpen={isModalOpen}
      setIsModalOpen={setIsModalOpen}
      props={{
        closable: false,
        maskClosable: false,
      }}
    >
      <div className="text-white">
        <div className="text-center mb-4 md:text-3xl text-2xl font-bold">
          Success!
        </div>
        <div className="text-center text-gray-100">
          Your payment process has been completed.
        </div>

        {/* Dotted line separator */}
        <div className="my-12 h-px bg-[repeating-linear-gradient(to_right,theme(colors.gray.100)_0,theme(colors.gray.600)_2px,transparent_2px,transparent_10px)]" />

        {/* Order details */}
        <div className="space-y-6">
          <div className="space-y-2">
            <div className="text-gray-100">Name</div>
            <div className="font-bold text-xl">
              LEGIT TOKEN - {tokenName || 0} $LEGIT TOKEN
            </div>
          </div>

          <div className="space-y-2 border-b border-gray-400 pb-4">
            <div className="text-gray-100">Reference Number</div>
            <div className="font-bold text-lg">#{tokenId}</div>
          </div>

          <div className="space-y-2">
            <div className="text-gray-100">Total Payment</div>
            <div className="flex justify-between items-center">
              <div className="font-bold text-lg">USD {amount || "0.00"}</div>
              <div>
                <Image
                  src="/icon_green_tick.png"
                  alt="success"
                  width={24}
                  height={24}
                />
              </div>
            </div>
          </div>
        </div>

        {/* confirm button */}
        <div className="mt-14">
          <button
            onClick={() => {
              setIsModalOpen(false);
              router.replace(router.asPath.split("?")[0]);
            }}
            className="w-full bg-transparent border border-white text-white font-medium py-3 px-6 rounded-lg"
          >
            CONFIRM
          </button>
        </div>
      </div>
    </AppModal>
  );
};

export default PaymentSuccessModal;
