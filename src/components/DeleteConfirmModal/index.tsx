import React from "react";
import { useIntl } from "react-intl";

import AppModal from "components/AppModal";

interface DeleteConfirmModalProps {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  content: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  icon?: React.ReactNode;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  title,
  content,
  confirmText = "confirm_text",
  cancelText = "cancel_text",
  onConfirm,
  onCancel,
  loading = false,
  icon,
}) => {
  const intl = useIntl();
  const handleCancel = () => {
    setIsModalOpen(false);
    onCancel && onCancel();
  };

  const handleConfirm = async () => {
    if (onConfirm) {
      await onConfirm();
    }
    setIsModalOpen(false);
  };

  const defaultIcon = (
    <div className="w-20 h-20 mx-auto flex items-center justify-center">
      <img
        src="/profile/icon_ox_gray.png"
        alt="icon_ox_gray"
        className="w-full h-full"
      />
    </div>
  );

  return (
    <AppModal
      isModalOpen={isModalOpen}
      setIsModalOpen={setIsModalOpen}
      title=""
      onCancel={handleCancel}
    >
      <div className="text-center py-4">
        {/* Icon */}
        {icon || defaultIcon}

        {/* Title */}
        <h2 className="text-white text-xl font-bold mb-4">
          {intl.formatMessage({ id: title })}
        </h2>

        {/* Content */}
        <p className="text-gray-100 text-sm leading-relaxed mb-8 px-2">
          {intl.formatMessage({ id: content })}
        </p>

        {/* Buttons */}
        <div className="space-y-3">
          {/* Confirm Button */}
          <button
            onClick={handleConfirm}
            disabled={loading}
            className="w-full bg-gradient-red text-white font-bold py-4 px-6 rounded-lg"
          >
            {loading
              ? intl.formatMessage({ id: "delete_account_loading" })
              : intl.formatMessage({ id: confirmText })}
          </button>

          {/* Cancel Button */}
          <button
            onClick={handleCancel}
            disabled={loading}
            className="w-full bg-transparent border border-gray-600 hover:border-gray-500 text-white font-medium py-4 px-6 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {intl.formatMessage({ id: cancelText })}
          </button>
        </div>
      </div>
    </AppModal>
  );
};

export default DeleteConfirmModal;
