import { IUser } from "./app";
import {
  IOrderItemBase,
  IServiceRequestImage,
  IServiceRequestResult,
  OrderItem,
} from "./orders";

export interface ICreditBalanceLog {
  id: number;
  user_id: number;
  type: string;
  credit_plan_snapshot: any;
  credit_plan_gift_card_id: any;
  credit_plan_gift_card_snapshot: any;
  transaction_id: any;
  legit_tag_order_id: any;
  service_request_id: number;
  marketplace_ad_id: any;
  lottery_event_id: any;
  lottery_ticket_count: any;
  referee_user_id: any;
  credit: string;
  remark: any;
  created_at: string;
  updated_at: string;
  service_request: IOrderItemBase;
}

export interface ITransaction {
  id: number;
  uuid: string;
  stripe_payment_intent_id: string;
  stripe_checkout_session_id: any;
  alipay_transaction_id: any;
  wxpay_transaction_id: any;
  user_id: number;
  credit_plan_snapshot: any;
  credit_plan_price: any;
  credit_plan_discount_snapshot: any;
  credit_plan_discount_id: any;
  credit_plan_discount: any;
  legit_tag_order_id: number;
  currency: string;
  amount: string;
  stripe_transaction_snapshot: string;
  stripe_transaction_fee_currency: string;
  stripe_transaction_fee: string;
  amount_exchange_rate: string;
  amount_hkd: string;
  amount_net: string;
  status: string;
  payment_method: string;
  payment_remark: any;
  payment_error: any;
  created_at: string;
  updated_at: string;
  legit_tag_order: LegitTagOrder;
}

export interface LegitTagOrder {
  id: number;
  uuid: string;
  user_id: number;
  legit_tag_plan_snapshot: LegitTagPlanSnapshot;
  legit_tag_type: string;
  legit_tag_plan_quantity: number;
  legit_tag_plan_credit: any;
  legit_tag_plan_currency: string;
  legit_tag_plan_price: string;
  legit_tag_quantity: number;
  legit_tag_shipping_snapshot: LegitTagShippingSnapshot;
  legit_tag_shipping_credit: any;
  legit_tag_shipping_currency: string;
  legit_tag_shipping_price: string;
  legit_tag_shipping_option: string;
  recipient_name: string;
  recipient_address1: string;
  recipient_address2: string;
  recipient_postcode: string;
  recipient_country: string;
  recipient_phone_country_code: string;
  recipient_phone_number: string;
  recipient_email: string;
  amount: string;
  status: string;
  legit_tag_uuids: any;
  remark_for_admin: any;
  remark_for_user: any;
  refunded: number;
  refunded_amount: string;
  created_at: string;
  updated_at: string;
}

export interface LegitTagPlanSnapshot {
  id: number;
  index: number;
  featured: number;
  business: number;
  type: string;
  title: string;
  title_tc: string;
  title_sc: string;
  title_es: any;
  title_ja: any;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  subtitle: any;
  subtitle_tc: any;
  subtitle_sc: any;
  subtitle_es: any;
  subtitle_ja: any;
  subtitle_ko: any;
  subtitle_vi: any;
  subtitle_th: any;
  subtitle_pl: any;
  description: any;
  description_tc: any;
  description_sc: any;
  description_es: any;
  description_ja: any;
  description_ko: any;
  description_vi: any;
  description_th: any;
  description_pl: any;
  quantity: number;
  credit_compared_at: any;
  credit: any;
  currency: string;
  price_compared_at: string;
  price: string;
  price_compared_at_cny: any;
  price_cny: string;
  logo_image_url: string;
  cover_image_url: any;
  default_selected: number;
  in_sale: number;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface LegitTagShippingSnapshot {
  id: number;
  index: number;
  featured: number;
  title: string;
  title_tc: string;
  title_sc: string;
  title_es: any;
  title_ja: any;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  subtitle: any;
  subtitle_tc: any;
  subtitle_sc: any;
  subtitle_es: any;
  subtitle_ja: any;
  subtitle_ko: any;
  subtitle_vi: any;
  subtitle_th: any;
  subtitle_pl: any;
  description: any;
  description_tc: any;
  description_sc: any;
  description_es: any;
  description_ja: any;
  description_ko: any;
  description_vi: any;
  description_th: any;
  description_pl: any;
  credit_compared_at: string;
  credit: string;
  currency: any;
  price_compared_at: any;
  price: any;
  price_compared_at_cny: any;
  price_cny: any;
  logo_image_url: any;
  cover_image_url: any;
  default_selected: number;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface ICaseDetail extends OrderItem {
  user: IUser;
  service_request_image: IServiceRequestImage[];
  service_request_result: IServiceRequestResult[];
  user_liked: boolean;
}
