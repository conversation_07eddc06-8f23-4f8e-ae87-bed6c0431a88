export interface IAppBanner {
  id: number;
  index: number;
  featured: number;
  title: string;
  subtitle: any;
  description: any;
  type: string;
  browser_type: string;
  page: string;
  cover_image_url: string;
  cover_image_url_tc: string;
  cover_image_url_sc: string;
  cover_image_url_es: any;
  cover_image_url_ja: any;
  cover_image_url_ko: any;
  cover_image_url_vi: any;
  cover_image_url_th: any;
  cover_image_url_pl: any;
  redirect_url: string;
  redirect_url_tc: string;
  redirect_url_sc: string;
  redirect_url_es: any;
  redirect_url_ja: string;
  redirect_url_ko: any;
  redirect_url_vi: any;
  redirect_url_th: any;
  redirect_url_pl: any;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IAppBrand {
  id: number;
  index: number;
  featured: number;
  title: string;
  title_tc: string | null;
  title_sc: string | null;
  title_es: string | null;
  title_ja: string | null;
  title_ko: string | null;
  title_vi: string | null;
  title_th: string | null;
  title_pl: string | null;
  slug: string;
  description: string;
  description_tc: string | null;
  description_sc: string | null;
  description_ja: string | null;
  description_pl: string | null;
  icon_image_url: string;
  cover_image_url: string;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IAppArticle {
  id: number;
  index: number;
  featured: number;
  language: string;
  article_type_id: number;
  title: string;
  subtitle: string;
  content: string;
  highlight: string;
  tag: string;
  tagline: any;
  slug: string;
  cover_image_url: string;
  og_image_url: any;
  sku_id: any;
  category_id: number;
  brand_id: number;
  model_id: any;
  public: number;
  enabled: number;
  published_at: string;
  created_at: string;
  updated_at: string;
}

export interface ICurrencyRate {
  id: number;
  index: number;
  currency_code: string;
  icon_image_url: string;
  currency_title: string;
  currency_title_tc: string;
  currency_title_sc: string;
  currency_title_es: any;
  currency_title_ja: any;
  currency_title_ko: any;
  currency_title_vi: any;
  currency_title_th: any;
  currency_title_pl: any;
  exchange_rate: string;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface ICaseItem {
  id: number;
  uuid: string;
  user_id: number;
  create_type: string;
  marketplace_ad_id: any;
  seller_service_request_id: any;
  legit_tag_id: any;
  legit_tag_uuid: any;
  legit_tag_credit_included: string;
  checker_id: any;
  service_set_id: number;
  service_guideline_set_id: number;
  category_id: number;
  brand_id: number;
  model_id: number;
  product_sku: any;
  product_sku_id: any;
  product_title: string;
  search_keyword: string;
  service_level_snapshot: string;
  service_level_id: number;
  service_level_credit: string;
  service_level_minute: number;
  service_extra_service_snapshot: string;
  service_extra_service_nft_certificate_credit: string;
  service_extra_service_insurance_credit: string;
  service_extra_service_code_checking_credit: string;
  service_extra_service_credit: string;
  service_request_promotion_snapshot: any;
  service_request_promotion_id: any;
  service_request_promotion_credit: any;
  credit_required: string;
  certificate_owner_name: any;
  user_custom_code: any;
  user_remark: any;
  admin_remark: any;
  status: string;
  comparison_report_status: string;
  result: "pass" | "not_pass";
  fake_rating: number;
  checker_count_required: number;
  checker_count_completed: number;
  result_conflict: number;
  service_set_updated: number;
  result_updated: number;
  insurance_handled: number;
  insurance_handled_remark: any;
  code_checking_enabled: number;
  code_checking_handled: number;
  code_checking_handled_remark: any;
  additional_photo_requested: number;
  check_delayed: number;
  cover_image_url: string;
  nft_certificate_image_url: any;
  code_checking_image_urls: any;
  user_unread_message_count: number;
  checker_unread_message_count: number;
  message_count: number;
  comment_count: number;
  like_count: number;
  product_source_type: any;
  product_source_remark: any;
  product_source_currency: string;
  product_source_price: any;
  user_review_overall: any;
  user_review_rating_speed: any;
  user_review_rating_result: any;
  user_review_rating_attitude: any;
  user_review_remark: any;
  user_read: number;
  user_read_at: string;
  refunded: number;
  service_extra_service_nft_certificate_refunded: number;
  service_extra_service_insurance_refunded: number;
  service_extra_service_code_checking_refunded: number;
  sellable: number;
  nft_certificate_minted: number;
  service_extra_service_purchased: number;
  service_extra_service_nft_certificate_purchased: number;
  service_extra_service_insurance_purchased: number;
  service_extra_service_code_checking_purchased: number;
  public: number;
  enabled: number;
  device: string;
  language: string;
  location: string;
  checker_lock_expired_at: any;
  expired_at: string;
  handled_at: string;
  completed_at: string;
  result_updated_at: any;
  insurance_handled_at: any;
  code_checking_handled_at: any;
  service_set_updated_at: any;
  nft_certificate_minted_at: any;
  created_at: string;
  updated_at: string;
  product_category: IProductCategory;
  product_brand: IProductBrand;
  product_model: IProductModel;
}
export interface ICaseHistory extends ICaseItem {
  user: IUser;
  user_liked: boolean;
}

export interface IBookmarkedItem extends ICaseItem {
  liked_at: string;
}

export interface IProductCategory {
  title: string;
  icon_image_url: string;
}

export interface IProductBrand {
  id: number;
  index: number;
  featured: number;
  title: string;
  title_tc: any;
  title_sc: any;
  title_es: any;
  title_ja: any;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  slug: string;
  description: string;
  description_tc: any;
  description_sc: any;
  description_ja: any;
  description_pl: any;
  icon_image_url: string;
  cover_image_url: string;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IProductModel {
  id: number;
  index: number;
  featured: number;
  category_id: number;
  brand_id: number;
  title: string;
  title_tc: any;
  title_sc: any;
  title_es: any;
  title_ja: any;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  icon_image_url: string;
  cover_image_url: any;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IUser {
  id: number;
  name: string;
  checker_title: any;
  username: string;
  ig_username: any;
  email: string;
  email_check: string;
  email_verified: number;
  seller_verified: number;
  seller_verification_in_review: number;
  seller_email_invited: number;
  role: string;
  checker_online: number;
  checker_base_salary_currency: string;
  checker_base_salary: any;
  checker_timetable_enabled: number;
  checker_warning: number;
  checker_remark: any;
  influencer: number;
  reseller: number;
  referrer_user_id: any;
  referral_code_used: any;
  referrer_commission_rate: string;
  referee_commission_rate: string;
  refer_success_count: number;
  vip_level: number;
  phone_country_code: any;
  phone_number: any;
  phone_verified: number;
  credit_balance: string;
  credit_commission_earned: string;
  credit_email_verification: string;
  service_request_count: number;
  service_request_cancelled_count: number;
  service_request_completed_count: number;
  service_request_pass_count: number;
  service_request_npass_count: number;
  service_request_unable_count: number;
  social_post_count: number;
  social_post_follower: number;
  social_post_following: number;
  social_post_description: string;
  last_request_created_at: any;
  last_certificate_owner_name: string;
  profile_image_url: any;
  date_of_birth: any;
  language: string;
  location: string;
  location_coordinate_latitude: any;
  location_coordinate_longitude: any;
  currency: string;
  apple_id: any;
  facebook_id: any;
  google_id: any;
  reddit_id: any;
  reddit_username: any;
  stripe_id: string;
  app_reviewed: number;
  instagram_reviewed: number;
  trustpilot_reviewed: number;
  marketplace_auto_reviewed: number;
  marketplace_bookmark_public: number;
  marketplace_ad_count: number;
  marketplace_rating: string;
  marketplace_rating_buyer: string;
  marketplace_rating_seller: string;
  marketplace_review: number;
  marketplace_review_buyer: number;
  marketplace_review_seller: number;
  marketplace_follower: number;
  marketplace_following: number;
  marketplace_default_radius_km: number;
  marketplace_description: any;
  last_ad_created_at: any;
  chatroom_unread: number;
  online: number;
  last_online_at: any;
  push_marketplace_bookmarked_listing_status_update: number;
  push_marketplace_buyer_authentication_reminder: number;
  push_marketplace_new_bookmark: number;
  push_marketplace_new_chat_message: number;
  push_marketplace_new_follower: number;
  push_marketplace_new_listing: number;
  push_marketplace_new_review: number;
  push_marketplace_offer_status_update: number;
  push_marketplace_price_discount_alert: number;
  push_marketplace_tip_and_promotion: number;
  email_marketplace_buyer_authentication_reminder: number;
  email_marketplace_new_chat_message: number;
  email_marketplace_offer_status_update: number;
  email_marketplace_price_discount_alert: number;
  email_marketplace_tip_and_promotion: number;
  email_marketplace_successful_listing: number;
  registration_uuid: string;
  delete_snapshot: any;
  feed_public: number;
  business_enabled: number;
  business_verified: number;
  system_generated: number;
  enabled: number;
  deleted_at: any;
  created_at: string;
  updated_at: string;
  user_category_ids: any[];
}

export interface ICategoryBrandModel extends IProductModel {
  product_brand: IProductBrand;
  product_category: IProductCategory;
}
