import {
  NEW_AUTHENTICATION_FORM_UPDATE,
  NEW_AUTHENTICATION_FORM_RESET,
  NEW_AUTHENTICATION_FORM_UPDATE_UPLOAD_PHOTO_LIST,
} from "../constants/actionTypes";
import { INewAuthenticationFormState } from "types/orders";

const initialState: INewAuthenticationFormState = {
  category_id: "",
  brand_id: "",
  model_id: "",
  serviceSet: null,
  currentServiceLevel: null,
  currentAdditionalServiceList: null,
  uploadPhotoList: [],
  certificateOwnerName: "",
  user_custom_code: "",
  user_remark: "",
  product_title: null,
  product_source_currency: "",
  isHasBox: true,
};

const newAuthenticationForm = (
  state: INewAuthenticationFormState = initialState,
  action: any
) => {
  switch (action.type) {
    case NEW_AUTHENTICATION_FORM_UPDATE:
      return {
        ...state,
        ...action.payload,
      };
    case NEW_AUTHENTICATION_FORM_UPDATE_UPLOAD_PHOTO_LIST:
      return {
        ...state,
        uploadPhotoList: action.updater(state.uploadPhotoList),
      };
    case NEW_AUTHENTICATION_FORM_RESET:
      return initialState;
    default:
      return state;
  }
};

export default newAuthenticationForm;
