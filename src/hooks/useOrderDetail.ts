import { useEffect } from "react";
import { RootState } from "reducers";

import { fetchOrderDetail, reset } from "actions/orderDetail";
import useAppSelector from "./useAppSelector";
import useAppDispatch from "./useAppDispatch";

interface UseOrderDetailParams {
  id: string | undefined;
  accessToken: string | undefined;
}

const useOrderDetail = ({ id, accessToken }: UseOrderDetailParams) => {
  const dispatch = useAppDispatch();
  const orderDetailState = useAppSelector(
    (state: RootState) => state.orderDetail
  );

  const { orderDetail, isFetchItemLoading, fetchItemErrors } = orderDetailState;

  useEffect(() => {
    if (accessToken && id) {
      dispatch(fetchOrderDetail({ id, accessToken }));
    }

    return () => {
      dispatch(reset());
    };
  }, [id, accessToken, dispatch]);

  const refetchOrderDetail = () => {
    if (accessToken && id) {
      dispatch(fetchOrderDetail({ id, accessToken }));
    }
  };

  return {
    orderDetail,
    isLoading: isFetchItemLoading,
    errors: fetchItemErrors,
    refetch: refetchOrderDetail,
  };
};

export default useOrderDetail;
