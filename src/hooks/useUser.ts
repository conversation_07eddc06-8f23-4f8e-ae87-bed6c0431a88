import {
  useEffect,
  useMemo,
  useCallback,
  useState,
  useLayoutEffect,
} from "react";
import { RootState } from "reducers";

import { fetchUser } from "actions/app";
import useAppSelector from "hooks/useAppSelector";
import useAppDispatch from "hooks/useAppDispatch";
import Storage from "utils/storage";
import { STORAGE_KEY } from "constants/app";
import { IUser } from "types/app";

interface UserState {
  accessToken: string | undefined;
  user: IUser | null;
  isFetchUserLoading: boolean;
}

interface UseUserHookReturn extends UserState {
  refetchUser: () => void;
}

const useUserHook = (): UseUserHookReturn => {
  const appState = useAppSelector((state: RootState) => state.app);
  const dispatch = useAppDispatch();
  const [storedUser, setStoredUser] = useState<any>(null);

  const { accessToken, user, isFetchUserLoading } = appState;

  useLayoutEffect(() => {
    if (user) return;

    const storedUserData = Storage.get(STORAGE_KEY.USER);
    setStoredUser(storedUserData);
  }, [user]);

  const fetchUserData = useCallback(() => {
    if (accessToken && !user) {
      dispatch(fetchUser({ accessToken }));
    }
  }, [accessToken, user, dispatch]);

  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  const refetchUser = useCallback(() => {
    if (accessToken) {
      dispatch(fetchUser({ accessToken }));
    }
  }, [accessToken, dispatch]);

  return useMemo(
    () => ({
      accessToken,
      user: user || storedUser,
      isFetchUserLoading,
      refetchUser,
    }),
    [accessToken, user, storedUser, isFetchUserLoading, refetchUser]
  );
};

export default useUserHook;
