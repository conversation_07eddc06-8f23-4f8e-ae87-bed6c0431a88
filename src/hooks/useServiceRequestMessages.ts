import { useEffect } from "react";
import { RootState } from "reducers";

import {
  fetchServiceRequestMessages,
  reset,
} from "actions/serviceRequestMessage";
import useAppSelector from "./useAppSelector";
import { useAppDispatch } from "./useAppDispatch";

interface UseServiceRequestMessagesParams {
  serviceRequestId: string;
  accessToken: string | undefined;
}

const useServiceRequestMessages = ({
  serviceRequestId,
  accessToken,
}: UseServiceRequestMessagesParams) => {
  const dispatch = useAppDispatch();
  const serviceRequestMessageState = useAppSelector(
    (state: RootState) => state.serviceRequestMessage
  );

  const { messages, isFetchItemsLoading, fetchItemsErrors, pagination } =
    serviceRequestMessageState;

  useEffect(() => {
    if (accessToken && serviceRequestId) {
      dispatch(
        fetchServiceRequestMessages({
          service_request_id: serviceRequestId,
          accessToken,
        })
      );
    }

    return () => {
      dispatch(reset());
    };
  }, [serviceRequestId, accessToken, dispatch]);

  const refetchMessages = () => {
    if (accessToken && serviceRequestId) {
      dispatch(
        fetchServiceRequestMessages({
          service_request_id: serviceRequestId,
          accessToken,
        })
      );
    }
  };

  return {
    messages,
    isLoading: isFetchItemsLoading,
    errors: fetchItemsErrors,
    pagination,
    refetch: refetchMessages,
  };
};

export default useServiceRequestMessages;
